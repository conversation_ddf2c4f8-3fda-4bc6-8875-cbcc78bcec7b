*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[19:51:39] 




[19:51:40] Extension host agent started.
[19:51:40] [<unknown>][edfe7e4b][ExtensionHostConnection] New connection established.
[19:51:40] [<unknown>][8f482383][ManagementConnection] New connection established.
[19:51:40] [<unknown>][edfe7e4b][ExtensionHostConnection] <1652> Launched Extension Host Process.
[19:51:40] ComputeTargetPlatform: linux-x64
[19:51:42] ComputeTargetPlatform: linux-x64
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:51:44] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:51:44] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:51:44] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:02] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:20] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:20] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:20] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:27] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:27] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:27] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
New EH opened, aborting shutdown
[19:56:40] New EH opened, aborting shutdown
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
[21:24:49] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:43:00] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:43:00] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:43:00] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:43:00] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:43:00] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:43:00] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
[22:08:43] [File Watcher (node.js)] Watcher shutdown because watched path got deleted
[00:13:45] [<unknown>][8f482383][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[00:13:45] [<unknown>][edfe7e4b][ExtensionHostConnection] <1652> Extension Host Process exited with code: 0, signal: null.
Last EH closed, waiting before shutting down
[00:13:45] Last EH closed, waiting before shutting down
[00:15:07] [<unknown>][2141f0d8][ManagementConnection] New connection established.
[00:15:07] [<unknown>][c3ecdef4][ExtensionHostConnection] New connection established.
[00:15:07] [<unknown>][c3ecdef4][ExtensionHostConnection] <13713> Launched Extension Host Process.
rejected promise not handled within 1 second: CodeExpectedError: Could not find pty 11 on pty host
stack trace: CodeExpectedError: Could not find pty 11 on pty host
    at O.W (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateTitle (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at R.s.<computed> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
[00:15:09] Error [CodeExpectedError]: Could not find pty 11 on pty host
    at O.W (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:6391)
    at O.updateTitle (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:46:1839)
    at R.s.<computed> (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:45:2933)
    at Object.call (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:28:4204)
    at ml.s (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:81286)
    at ml.q (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80809)
    at ps.value (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:80211)
    at E.B (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2373)
    at E.fire (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:26:2591)
    at process.C (file:///home/<USER>/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js:24:30052)
    at process.emit (node:events:518:28)
    at emit (node:internal/child_process:949:14)
    at process.processTicksAndRejections (node:internal/process/task_queues:91:21)
