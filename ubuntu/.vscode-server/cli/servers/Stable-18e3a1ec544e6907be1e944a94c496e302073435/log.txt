*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[19:51:39] 




[19:51:40] Extension host agent started.
[19:51:40] [<unknown>][edfe7e4b][ExtensionHostConnection] New connection established.
[19:51:40] [<unknown>][8f482383][ManagementConnection] New connection established.
[19:51:40] [<unknown>][edfe7e4b][ExtensionHostConnection] <1652> Launched Extension Host Process.
[19:51:40] ComputeTargetPlatform: linux-x64
[19:51:42] ComputeTargetPlatform: linux-x64
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:51:44] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:51:44] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:51:44] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:02] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:20] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:20] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:20] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:27] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:27] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[19:52:27] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
New EH opened, aborting shutdown
[19:56:40] New EH opened, aborting shutdown
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8502
stack trace: Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
[21:09:32] Error: connect ECONNREFUSED 127.0.0.1:8502
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 8502
}
