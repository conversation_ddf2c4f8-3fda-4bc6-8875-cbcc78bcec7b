[["/home/<USER>/start.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/start.sh"}}], ["/home/<USER>/stop_all.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/stop_all.py"}}], ["/home/<USER>/run_dashboard.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/run_dashboard.py"}}], ["/home/<USER>/git/run_dashboard.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/git/run_dashboard.py"}}], ["/home/<USER>/git/nginx-config/HOSTINGER-DNS-SETUP.md", {"value": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/HOSTINGER-DNS-SETUP.md"}}], ["/home/<USER>/git/nginx-config/deploy-secure-dashboard.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/deploy-secure-dashboard.sh"}}], ["/home/<USER>/git/nginx-config/deploy-dashboard.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/deploy-dashboard.sh"}}], ["/home/<USER>/git/nginx-config/deploy-new-client.sh", {"value": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/deploy-new-client.sh"}}], ["/home/<USER>/git/nginx-config/dashboard.algofactory.in.conf", {"value": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}], ["/home/<USER>/git/dashboard/algofactory_updater.log", {"value": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/algofactory_updater.log"}}], ["/home/<USER>/git/dashboard/auth_manager.py", {"value": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/auth_manager.py"}}], ["/home/<USER>/git/dashboard/clients_data.json", {"value": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/clients_data.json"}}], ["/home/<USER>/git/dashboard/auth_config.json", {"value": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/auth_config.json"}}], ["/home/<USER>/git/nginx-config/1013.algofactory.in.conf", {"value": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/1013.algofactory.in.conf"}}]]