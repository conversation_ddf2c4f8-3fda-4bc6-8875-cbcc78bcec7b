[["d3a16f9f-e9d1-440a-843d-e317cb503ddb", {"value": {"selectedCode": "", "prefix": "#!/usr/bin/env python3\n", "suffix": "\"\"\"\nAlgoFactory Dashboard Launcher\nSimple script to run the AlgoFactory management dashboard with Streamlit.\n\"\"\"\n\nimport os\nimport sys\nimport subprocess\nfrom pathlib import Path\n\ndef run_dashboard():\n    \"\"\"Run the AlgoFactory dashboard with Streamlit.\"\"\"\n    \n    # Get the current directory\n    current_dir = Path(__file__).parent.absolute()\n    dashboard_dir = current_dir / \"dashboard\"\n    \n    # Check if dashboard directory exists\n    if not dashboard_dir.exists():\n        print(\"❌ Dashboard directory not found!\")\n        print(f\"Expected: {dashboard_dir}\")\n        return False\n    \n    # Check if simple_dashboard.py exists\n    dashboard_file = dashboard_dir / \"simple_dashboard.py\"\n    if not dashboard_file.exists():\n        print(\"❌ Dashboard file not found!\")\n        print(f\"Expected: {dashboard_file}\")\n        return False\n    \n    print(\"🚀 Starting AlgoFactory Dashboard...\")\n    print(f\"📁 Dashboard directory: {dashboard_dir}\")\n    print(f\"🌐 URL: http://localhost:8502\")\n    print(\"🎨 New Full-Screen Layout (Hostinger-Style)\")\n    print(\"📊 Features: Sidebar Navigation, Full-Width Tables, Professional Design\")\n    print(\"=\" * 50)\n    \n    try:\n        # Change to dashboard directory\n        os.chdir(dashboard_dir)\n        \n        # Run Streamlit\n        cmd = [\n            sys.executable, \"-m\", \"streamlit\", \"run\", \n            \"simple_dashboard.py\",\n            \"--server.port\", \"8502\",\n            \"--server.address\", \"localhost\",\n            \"--server.headless\", \"true\"\n        ]\n        \n        print(\"💡 Starting Streamlit server...\")\n        print(\"💡 Press Ctrl+C to stop the dashboard\")\n        print(\"=\" * 50)\n        \n        # Run the command\n        subprocess.run(cmd, check=True)\n        \n    except KeyboardInterrupt:\n        print(\"\\n👋 Dashboard stopped by user\")\n        return True\n    except subprocess.CalledProcessError as e:\n        print(f\"❌ Error running Streamlit: {e}\")\n        return False\n    except Exception as e:\n        print(f\"❌ Unexpected error: {e}\")\n        return False\n\ndef main():\n    \"\"\"Main function.\"\"\"\n    print(\"🏭 AlgoFactory Dashboard Launcher\")\n    print(\"🎨 Full-Screen Professional Layout\")\n    print(\"=\" * 50)\n    \n    # Check if streamlit is installed\n    try:\n        import streamlit\n        print(f\"✅ Streamlit version: {streamlit.__version__}\")\n    except ImportError:\n        print(\"❌ Streamlit not installed!\")\n        print(\"💡 Install with: pip install streamlit\")\n        return\n    \n    # Run the dashboard\n    success = run_dashboard()\n    \n    if success:\n        print(\"\\n✅ Dashboard session completed\")\n    else:\n        print(\"\\n❌ Dashboard failed to start\")\n\nif __name__ == \"__main__\":\n    main()\n", "path": "ubuntu/git/run_dashboard.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}], ["86e5603d-f393-434f-ac9c-cb0a8ec34dc9", {"value": {"selectedCode": "", "prefix": "{\n", "suffix": "  \"users\": {},\n  \"failed_attempts\": {},\n  \"settings\": {\n    \"session_timeout\": 3600,\n    \"max_login_attempts\": 5,\n    \"lockout_duration\": 900,\n    \"require_strong_passwords\": true,\n    \"enable_2fa\": false\n  }\n}", "path": "ubuntu/git/dashboard/auth_config.json", "language": "json", "prefixBegin": 0, "suffixEnd": 1}}], ["9e893e3a-918b-4286-a599-b695fe002721", {"value": {"selectedCode": "", "prefix": "{\n", "suffix": "  \"users\": {\n    \"admin\": {\n      \"password_hash\": \"$2b$12$irvCWwdqWrur5YqaDqXaqOcB0clgt.zXF7LTyZoI/aZqF0/inqMT6\",\n      \"role\": \"admin\",\n      \"email\": \"<EMAIL>\",\n      \"created_at\": \"2025-06-22T17:59:12.451221\",\n      \"last_login\": \"2025-06-22T18:07:11.143434\",\n      \"active\": true\n    }\n  },\n  \"failed_attempts\": {},\n  \"settings\": {\n    \"session_timeout\": 3600,\n    \"max_login_attempts\": 5,\n    \"lockout_duration\": 900,\n    \"require_strong_passwords\": true,\n    \"enable_2fa\": false\n  }\n}", "path": "ubuntu/git/dashboard/auth_config.json", "language": "json", "prefixBegin": 0, "suffixEnd": 1}}], ["f7b5c4de-d4f6-49b3-931e-421fc88d0391", {"value": {"selectedCode": "", "prefix": "{\n", "suffix": "  \"users\": {\n    \"admin\": {\n      \"password_hash\": \"$2b$12$irvCWwdqWrur5YqaDqXaqOcB0clgt.zXF7LTyZoI/aZqF0/inqMT6\",\n      \"role\": \"admin\",\n      \"email\": \"<EMAIL>\",\n      \"created_at\": \"2025-06-22T17:59:12.451221\",\n      \"last_login\": \"2025-06-22T18:23:16.780161\",\n      \"active\": true\n    }\n  },\n  \"failed_attempts\": {},\n  \"settings\": {\n    \"session_timeout\": 3600,\n    \"max_login_attempts\": 5,\n    \"lockout_duration\": 900,\n    \"require_strong_passwords\": true,\n    \"enable_2fa\": false\n  }\n}", "path": "ubuntu/git/dashboard/auth_config.json", "language": "json", "prefixBegin": 0, "suffixEnd": 1}}], ["0c5cacf6-ecab-4b2e-a41f-27b64cd68b16", {"value": {"selectedCode": "", "prefix": "# Broker Configuration\r\n", "suffix": "BROKER_API_KEY = 'MZA0cLWq'\r\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\r\n\r\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\r\n\r\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\r\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\r\n\r\nREDIRECT_URL = 'https://5000.algofactory.in/angel/callback'  # Updated for domain\r\n\r\n# Valid Brokers Configuration\r\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\r\n\r\n# Security Configuration\r\n# IMPORTANT: Generate new random values for both keys during setup!\r\n\r\n# AlgoFactory Application Key\r\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\r\n\r\n# Security Pepper - Used for hashing/encryption of sensitive data\r\n# This is used for:\r\n# 1. API key hashing\r\n# 2. User password hashing\r\n# 3. Broker auth token encryption\r\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\r\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\r\n\r\n# AlgoFactory Database Configuration\r\nDATABASE_URL = 'sqlite:///db/openalgo.db' \r\n\r\n# AlgoFactory Ngrok Configuration\r\nNGROK_ALLOW = 'FALSE' \r\n\r\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\r\n# Change to your custom domain or Ngrok domain\r\nHOST_SERVER = 'https://5000.algofactory.in'\r\n\r\n# AlgoFactory Flask App Host and Port Configuration\r\n# For 0.0.0.0 (accessible from other devices on the network)\r\n# Flask Environment - development or production\r\nFLASK_HOST_IP='0.0.0.0'\r\nFLASK_PORT='5000'\r\nFLASK_DEBUG='False'\r\nFLASK_ENV='production'\r\n\r\n# WebSocket Configuration\r\nWEBSOCKET_HOST='localhost'\r\nWEBSOCKET_PORT='8765'\r\nWEBSOCKET_URL='ws://localhost:8765'\r\n\r\n# ZeroMQ Configuration\r\nZMQ_HOST='localhost'\r\nZMQ_PORT='5555'\r\n\r\n# AlgoFactory Rate Limit Settings\r\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\" \r\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\r\nAPI_RATE_LIMIT=\"10 per second\"\r\n\r\n# AlgoFactory API Configuration\r\n\r\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\r\n# Single legged orders are not affected by this setting.\r\nSMART_ORDER_DELAY = '0.5'\r\n\r\n# Session Expiry Time (24-hour format, IST)\r\n# All user sessions will automatically expire at this time daily\r\nSESSION_EXPIRY_TIME = '03:00'\r\n\r\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\r\n# Set to TRUE to enable CORS support, FALSE to disable\r\nCORS_ENABLED = 'TRUE'\r\n\r\n# Comma-separated list of allowed origins (domains)\r\n# Example: http://localhost:3000,https://example.com\r\n# Use '*' to allow all origins (not recommended for production)\r\nCORS_ALLOWED_ORIGINS = 'https://5000.algofactory.in'\r\n\r\n# Comma-separated list of allowed HTTP methods\r\n# Default: GET,POST\r\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\r\n\r\n# Comma-separated list of allowed headers\r\n# Default Flask-CORS values will be used if not specified\r\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\r\n\r\n# Comma-separated list of headers exposed to the browser\r\nCORS_EXPOSED_HEADERS = ''\r\n\r\n# Whether to allow credentials (cookies, authorization headers)\r\n# Set to TRUE only if you need to support credentials\r\nCORS_ALLOW_CREDENTIALS = 'FALSE'\r\n\r\n# Max age (in seconds) for browser to cache preflight requests\r\n# Default: 86400 (24 hours)\r\nCORS_MAX_AGE = '86400'\r\n\r\n# AlgoFactory Content Security Policy (CSP) Configuration\r\n# Set to TRUE to enable CSP, FALSE to disable\r\nCSP_ENABLED = 'TRUE'\r\n\r\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\r\n# This will report violations but not block content\r\nCSP_REPORT_ONLY = 'FALSE'\r\n\r\n# Default source directive - restricts all resource types by default\r\nCSP_DEFAULT_SRC = \"'self'\"\r\n\r\n# Script source directive - controls where scripts can be loaded from\r\n# Includes Socket.IO CDN which is required by the application\r\n# 'unsafe-inline' is needed for Socket.IO to function properly\r\n# Cloudflare Insights is used for analytics\r\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\r\n\r\n# Style source directive - controls where styles can be loaded from\r\n# 'unsafe-inline' is needed for some inline styles in the application\r\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\r\n\r\n# Image source directive - controls where images can be loaded from\r\n# 'data:' allows base64 encoded images\r\nCSP_IMG_SRC = \"'self' data:\"\r\n\r\n# Connect source directive - controls what network connections are allowed\r\n# Includes WebSocket connections needed for real-time updates\r\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\r\n\r\n# Font source directive - controls where fonts can be loaded from\r\nCSP_FONT_SRC = \"'self'\"\r\n\r\n# Object source directive - controls where plugins can be loaded from\r\n# 'none' disables all object, embed, and applet elements\r\nCSP_OBJECT_SRC = \"'none'\"\r\n\r\n# Media source directive - controls where audio and video can be loaded from\r\n# Allows audio alerts from your domain and potentially CDN sources in the future\r\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\r\n\r\n# Frame source directive - controls where iframes can be loaded from\r\n# If you integrate with TradingView or other platforms, you may need to add their domains\r\nCSP_FRAME_SRC = \"'self'\"\r\n\r\n# Form action directive - restricts where forms can be submitted to\r\nCSP_FORM_ACTION = \"'self'\"\r\n\r\n# Frame ancestors directive - controls which sites can embed your site in frames\r\n# This helps prevent clickjacking attacks\r\nCSP_FRAME_ANCESTORS = \"'self'\"\r\n\r\n# Base URI directive - restricts what base URIs can be used\r\nCSP_BASE_URI = \"'self'\"\r\n\r\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\r\n# Recommended for production environments\r\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\r\n\r\n# URI to report CSP violations to (optional)\r\n# Example: /csp-report\r\nCSP_REPORT_URI = ''\r\n\r\n# CSRF (Cross-Site Request Forgery) Protection Configuration\r\n# Set to TRUE to enable CSRF protection, FALSE to disable\r\nCSRF_ENABLED = 'TRUE'\r\n\r\n# CSRF Token Time Limit (in seconds)\r\n# Leave empty for no time limit (tokens valid for entire session)\r\n# Example: 3600 = 1 hour, 86400 = 24 hours\r\nCSRF_TIME_LIMIT = ''\r\n", "path": "ubuntu/git/algofactory/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["e6b8ea2e-0726-4d41-81c2-a9cfeb284a3b", {"value": {"selectedCode": "", "prefix": "# Broker Configuration\r\n", "suffix": "BROKER_API_KEY = 'MZA0cLWq'\r\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\r\n\r\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\r\n\r\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\r\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\r\n\r\nREDIRECT_URL = 'https://5000.algofactory.in/angel/callback'  # Updated for domain\r\n\r\n# Valid Brokers Configuration\r\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\r\n\r\n# Security Configuration\r\n# IMPORTANT: Generate new random values for both keys during setup!\r\n\r\n# AlgoFactory Application Key\r\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\r\n\r\n# Security Pepper - Used for hashing/encryption of sensitive data\r\n# This is used for:\r\n# 1. API key hashing\r\n# 2. User password hashing\r\n# 3. Broker auth token encryption\r\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\r\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\r\n\r\n# AlgoFactory Database Configuration\r\nDATABASE_URL = 'sqlite:///db/openalgo.db' \r\n\r\n# AlgoFactory Ngrok Configuration\r\nNGROK_ALLOW = 'FALSE' \r\n\r\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\r\n# Change to your custom domain or Ngrok domain\r\nHOST_SERVER = 'https://5000.algofactory.in'\r\n\r\n# AlgoFactory Flask App Host and Port Configuration\r\n# For 0.0.0.0 (accessible from other devices on the network)\r\n# Flask Environment - development or production\r\nFLASK_HOST_IP='0.0.0.0'\r\nFLASK_PORT='5000'\r\nFLASK_DEBUG='False'\r\nFLASK_ENV='production'\r\n\r\n# WebSocket Configuration\r\nWEBSOCKET_HOST='localhost'\r\nWEBSOCKET_PORT='8765'\r\nWEBSOCKET_URL='ws://localhost:8765'\r\n\r\n# ZeroMQ Configuration\r\nZMQ_HOST='localhost'\r\nZMQ_PORT='5555'\r\n\r\n# AlgoFactory Rate Limit Settings\r\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\" \r\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\r\nAPI_RATE_LIMIT=\"10 per second\"\r\n\r\n# AlgoFactory API Configuration\r\n\r\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\r\n# Single legged orders are not affected by this setting.\r\nSMART_ORDER_DELAY = '0.5'\r\n\r\n# Session Expiry Time (24-hour format, IST)\r\n# All user sessions will automatically expire at this time daily\r\nSESSION_EXPIRY_TIME = '03:00'\r\n\r\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\r\n# Set to TRUE to enable CORS support, FALSE to disable\r\nCORS_ENABLED = 'TRUE'\r\n\r\n# Comma-separated list of allowed origins (domains)\r\n# Example: http://localhost:3000,https://example.com\r\n# Use '*' to allow all origins (not recommended for production)\r\nCORS_ALLOWED_ORIGINS = 'https://5000.algofactory.in'\r\n\r\n# Comma-separated list of allowed HTTP methods\r\n# Default: GET,POST\r\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\r\n\r\n# Comma-separated list of allowed headers\r\n# Default Flask-CORS values will be used if not specified\r\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\r\n\r\n# Comma-separated list of headers exposed to the browser\r\nCORS_EXPOSED_HEADERS = ''\r\n\r\n# Whether to allow credentials (cookies, authorization headers)\r\n# Set to TRUE only if you need to support credentials\r\nCORS_ALLOW_CREDENTIALS = 'FALSE'\r\n\r\n# Max age (in seconds) for browser to cache preflight requests\r\n# Default: 86400 (24 hours)\r\nCORS_MAX_AGE = '86400'\r\n\r\n# AlgoFactory Content Security Policy (CSP) Configuration\r\n# Set to TRUE to enable CSP, FALSE to disable\r\nCSP_ENABLED = 'TRUE'\r\n\r\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\r\n# This will report violations but not block content\r\nCSP_REPORT_ONLY = 'FALSE'\r\n\r\n# Default source directive - restricts all resource types by default\r\nCSP_DEFAULT_SRC = \"'self'\"\r\n\r\n# Script source directive - controls where scripts can be loaded from\r\n# Includes Socket.IO CDN which is required by the application\r\n# 'unsafe-inline' is needed for Socket.IO to function properly\r\n# Cloudflare Insights is used for analytics\r\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\r\n\r\n# Style source directive - controls where styles can be loaded from\r\n# 'unsafe-inline' is needed for some inline styles in the application\r\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\r\n\r\n# Image source directive - controls where images can be loaded from\r\n# 'data:' allows base64 encoded images\r\nCSP_IMG_SRC = \"'self' data:\"\r\n\r\n# Connect source directive - controls what network connections are allowed\r\n# Includes WebSocket connections needed for real-time updates\r\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\r\n\r\n# Font source directive - controls where fonts can be loaded from\r\nCSP_FONT_SRC = \"'self'\"\r\n\r\n# Object source directive - controls where plugins can be loaded from\r\n# 'none' disables all object, embed, and applet elements\r\nCSP_OBJECT_SRC = \"'none'\"\r\n\r\n# Media source directive - controls where audio and video can be loaded from\r\n# Allows audio alerts from your domain and potentially CDN sources in the future\r\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\r\n\r\n# Frame source directive - controls where iframes can be loaded from\r\n# If you integrate with TradingView or other platforms, you may need to add their domains\r\nCSP_FRAME_SRC = \"'self'\"\r\n\r\n# Form action directive - restricts where forms can be submitted to\r\nCSP_FORM_ACTION = \"'self'\"\r\n\r\n# Frame ancestors directive - controls which sites can embed your site in frames\r\n# This helps prevent clickjacking attacks\r\nCSP_FRAME_ANCESTORS = \"'self'\"\r\n\r\n# Base URI directive - restricts what base URIs can be used\r\nCSP_BASE_URI = \"'self'\"\r\n\r\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\r\n# Recommended for production environments\r\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\r\n\r\n# URI to report CSP violations to (optional)\r\n# Example: /csp-report\r\nCSP_REPORT_URI = ''\r\n\r\n# CSRF (Cross-Site Request Forgery) Protection Configuration\r\n# Set to TRUE to enable CSRF protection, FALSE to disable\r\nCSRF_ENABLED = 'TRUE'\r\n\r\n# CSRF Token Time Limit (in seconds)\r\n# Leave empty for no time limit (tokens valid for entire session)\r\n# Example: 3600 = 1 hour, 86400 = 24 hours\r\nCSRF_TIME_LIMIT = ''\r\n", "path": "ubuntu/git/algofactory/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["0cf6b375-6ce0-43bf-b8d3-9d600649288b", {"value": {"selectedCode": "", "prefix": "# Broker Configuration\r\n", "suffix": "BROKER_API_KEY = 'MZA0cLWq'\r\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\r\n\r\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\r\n\r\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\r\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\r\n\r\nREDIRECT_URL = 'https://5000.algofactory.in/angel/callback'  # Updated for domain\r\n\r\n# Valid Brokers Configuration\r\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\r\n\r\n# Security Configuration\r\n# IMPORTANT: Generate new random values for both keys during setup!\r\n\r\n# AlgoFactory Application Key\r\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\r\n\r\n# Security Pepper - Used for hashing/encryption of sensitive data\r\n# This is used for:\r\n# 1. API key hashing\r\n# 2. User password hashing\r\n# 3. Broker auth token encryption\r\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\r\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\r\n\r\n# AlgoFactory Database Configuration\r\nDATABASE_URL = 'sqlite:///db/openalgo.db' \r\n\r\n# AlgoFactory Ngrok Configuration\r\nNGROK_ALLOW = 'FALSE' \r\n\r\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\r\n# Change to your custom domain or Ngrok domain\r\nHOST_SERVER = 'https://5000.algofactory.in'\r\n\r\n# AlgoFactory Flask App Host and Port Configuration\r\n# For 0.0.0.0 (accessible from other devices on the network)\r\n# Flask Environment - development or production\r\nFLASK_HOST_IP='0.0.0.0'\r\nFLASK_PORT='5000'\r\nFLASK_DEBUG='False'\r\nFLASK_ENV='production'\r\n\r\n# WebSocket Configuration\r\nWEBSOCKET_HOST='localhost'\r\nWEBSOCKET_PORT='8765'\r\nWEBSOCKET_URL='ws://localhost:8765'\r\n\r\n# ZeroMQ Configuration\r\nZMQ_HOST='localhost'\r\nZMQ_PORT='5555'\r\n\r\n# AlgoFactory Rate Limit Settings\r\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\" \r\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\r\nAPI_RATE_LIMIT=\"10 per second\"\r\n\r\n# AlgoFactory API Configuration\r\n\r\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\r\n# Single legged orders are not affected by this setting.\r\nSMART_ORDER_DELAY = '0.5'\r\n\r\n# Session Expiry Time (24-hour format, IST)\r\n# All user sessions will automatically expire at this time daily\r\nSESSION_EXPIRY_TIME = '03:00'\r\n\r\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\r\n# Set to TRUE to enable CORS support, FALSE to disable\r\nCORS_ENABLED = 'TRUE'\r\n\r\n# Comma-separated list of allowed origins (domains)\r\n# Example: http://localhost:3000,https://example.com\r\n# Use '*' to allow all origins (not recommended for production)\r\nCORS_ALLOWED_ORIGINS = 'https://5000.algofactory.in'\r\n\r\n# Comma-separated list of allowed HTTP methods\r\n# Default: GET,POST\r\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\r\n\r\n# Comma-separated list of allowed headers\r\n# Default Flask-CORS values will be used if not specified\r\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\r\n\r\n# Comma-separated list of headers exposed to the browser\r\nCORS_EXPOSED_HEADERS = ''\r\n\r\n# Whether to allow credentials (cookies, authorization headers)\r\n# Set to TRUE only if you need to support credentials\r\nCORS_ALLOW_CREDENTIALS = 'FALSE'\r\n\r\n# Max age (in seconds) for browser to cache preflight requests\r\n# Default: 86400 (24 hours)\r\nCORS_MAX_AGE = '86400'\r\n\r\n# AlgoFactory Content Security Policy (CSP) Configuration\r\n# Set to TRUE to enable CSP, FALSE to disable\r\nCSP_ENABLED = 'TRUE'\r\n\r\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\r\n# This will report violations but not block content\r\nCSP_REPORT_ONLY = 'FALSE'\r\n\r\n# Default source directive - restricts all resource types by default\r\nCSP_DEFAULT_SRC = \"'self'\"\r\n\r\n# Script source directive - controls where scripts can be loaded from\r\n# Includes Socket.IO CDN which is required by the application\r\n# 'unsafe-inline' is needed for Socket.IO to function properly\r\n# Cloudflare Insights is used for analytics\r\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\r\n\r\n# Style source directive - controls where styles can be loaded from\r\n# 'unsafe-inline' is needed for some inline styles in the application\r\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\r\n\r\n# Image source directive - controls where images can be loaded from\r\n# 'data:' allows base64 encoded images\r\nCSP_IMG_SRC = \"'self' data:\"\r\n\r\n# Connect source directive - controls what network connections are allowed\r\n# Includes WebSocket connections needed for real-time updates\r\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\r\n\r\n# Font source directive - controls where fonts can be loaded from\r\nCSP_FONT_SRC = \"'self'\"\r\n\r\n# Object source directive - controls where plugins can be loaded from\r\n# 'none' disables all object, embed, and applet elements\r\nCSP_OBJECT_SRC = \"'none'\"\r\n\r\n# Media source directive - controls where audio and video can be loaded from\r\n# Allows audio alerts from your domain and potentially CDN sources in the future\r\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\r\n\r\n# Frame source directive - controls where iframes can be loaded from\r\n# If you integrate with TradingView or other platforms, you may need to add their domains\r\nCSP_FRAME_SRC = \"'self'\"\r\n\r\n# Form action directive - restricts where forms can be submitted to\r\nCSP_FORM_ACTION = \"'self'\"\r\n\r\n# Frame ancestors directive - controls which sites can embed your site in frames\r\n# This helps prevent clickjacking attacks\r\nCSP_FRAME_ANCESTORS = \"'self'\"\r\n\r\n# Base URI directive - restricts what base URIs can be used\r\nCSP_BASE_URI = \"'self'\"\r\n\r\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\r\n# Recommended for production environments\r\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\r\n\r\n# URI to report CSP violations to (optional)\r\n# Example: /csp-report\r\nCSP_REPORT_URI = ''\r\n\r\n# CSRF (Cross-Site Request Forgery) Protection Configuration\r\n# Set to TRUE to enable CSRF protection, FALSE to disable\r\nCSRF_ENABLED = 'TRUE'\r\n\r\n# CSRF Token Time Limit (in seconds)\r\n# Leave empty for no time limit (tokens valid for entire session)\r\n# Example: 3600 = 1 hour, 86400 = 24 hours\r\nCSRF_TIME_LIMIT = ''\r\n", "path": "ubuntu/git/algofactory/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}], ["ae926c94-26c0-4465-9d52-fbe536256ece", {"value": {"selectedCode": "", "prefix": "# Broker Configuration\r\n", "suffix": "BROKER_API_KEY = 'MZA0cLWq'\r\nBROKER_API_SECRET = 'XIA6RJ3HPG4ZRKKYJLIZ6ROKAM'\r\n\r\n# Market Data Configuration (Optional and Required only for XTS API Supported Brokers)\r\n\r\nBROKER_API_KEY_MARKET = 'YOUR_BROKER_MARKET_API_KEY'\r\nBROKER_API_SECRET_MARKET = 'YOUR_BROKER_MARKET_API_SECRET'\r\n\r\nREDIRECT_URL = 'https://5000.algofactory.in/angel/callback'  # Updated for domain\r\n\r\n# Valid Brokers Configuration\r\nVALID_BROKERS = 'fivepaisa,fivepaisaxts,aliceblue,angel,compositedge,dhan,dhan_sandbox,firstock,flattrade,fyers,groww,iifl,kotak,jainam,jainampro,paytm,pocketful,shoonya,tradejini,upstox,wisdom,zebu,zerodha'\r\n\r\n# Security Configuration\r\n# IMPORTANT: Generate new random values for both keys during setup!\r\n\r\n# AlgoFactory Application Key\r\nAPP_KEY = '3daa0403ce2501ee7432b75bf100048e3cf510d63d2754f952e93d88bf07ea84'\r\n\r\n# Security Pepper - Used for hashing/encryption of sensitive data\r\n# This is used for:\r\n# 1. API key hashing\r\n# 2. User password hashing\r\n# 3. Broker auth token encryption\r\n# Generate a new random string during setup using: python -c \"import secrets; print(secrets.token_hex(32))\"\r\nAPI_KEY_PEPPER = 'a25d94718479b170c16278e321ea6c989358bf499a658fd20c90033cef8ce772'\r\n\r\n# AlgoFactory Database Configuration\r\nDATABASE_URL = 'sqlite:///db/openalgo.db' \r\n\r\n# AlgoFactory Ngrok Configuration\r\nNGROK_ALLOW = 'FALSE' \r\n\r\n# AlgoFactory Hosted Server (Custom Domain Name) or Ngrok Domain Configuration\r\n# Change to your custom domain or Ngrok domain\r\nHOST_SERVER = 'https://5000.algofactory.in'\r\n\r\n# AlgoFactory Flask App Host and Port Configuration\r\n# For 0.0.0.0 (accessible from other devices on the network)\r\n# Flask Environment - development or production\r\nFLASK_HOST_IP='0.0.0.0'\r\nFLASK_PORT='5000'\r\nFLASK_DEBUG='False'\r\nFLASK_ENV='production'\r\n\r\n# WebSocket Configuration\r\nWEBSOCKET_HOST='localhost'\r\nWEBSOCKET_PORT='8765'\r\nWEBSOCKET_URL='ws://localhost:8765'\r\n\r\n# ZeroMQ Configuration\r\nZMQ_HOST='localhost'\r\nZMQ_PORT='5555'\r\n\r\n# AlgoFactory Rate Limit Settings\r\nLOGIN_RATE_LIMIT_MIN = \"5 per minute\" \r\nLOGIN_RATE_LIMIT_HOUR = \"25 per hour\"\r\nAPI_RATE_LIMIT=\"10 per second\"\r\n\r\n# AlgoFactory API Configuration\r\n\r\n# Required to give 0.5 second to 1 second delay between multi-legged option strategies\r\n# Single legged orders are not affected by this setting.\r\nSMART_ORDER_DELAY = '0.5'\r\n\r\n# Session Expiry Time (24-hour format, IST)\r\n# All user sessions will automatically expire at this time daily\r\nSESSION_EXPIRY_TIME = '03:00'\r\n\r\n# AlgoFactory CORS (Cross-Origin Resource Sharing) Configuration\r\n# Set to TRUE to enable CORS support, FALSE to disable\r\nCORS_ENABLED = 'TRUE'\r\n\r\n# Comma-separated list of allowed origins (domains)\r\n# Example: http://localhost:3000,https://example.com\r\n# Use '*' to allow all origins (not recommended for production)\r\nCORS_ALLOWED_ORIGINS = 'https://5000.algofactory.in'\r\n\r\n# Comma-separated list of allowed HTTP methods\r\n# Default: GET,POST\r\nCORS_ALLOWED_METHODS = 'GET,POST,DELETE,PUT,PATCH'\r\n\r\n# Comma-separated list of allowed headers\r\n# Default Flask-CORS values will be used if not specified\r\nCORS_ALLOWED_HEADERS = 'Content-Type,Authorization,X-Requested-With'\r\n\r\n# Comma-separated list of headers exposed to the browser\r\nCORS_EXPOSED_HEADERS = ''\r\n\r\n# Whether to allow credentials (cookies, authorization headers)\r\n# Set to TRUE only if you need to support credentials\r\nCORS_ALLOW_CREDENTIALS = 'FALSE'\r\n\r\n# Max age (in seconds) for browser to cache preflight requests\r\n# Default: 86400 (24 hours)\r\nCORS_MAX_AGE = '86400'\r\n\r\n# AlgoFactory Content Security Policy (CSP) Configuration\r\n# Set to TRUE to enable CSP, FALSE to disable\r\nCSP_ENABLED = 'TRUE'\r\n\r\n# Set to TRUE to use Content-Security-Policy-Report-Only mode (testing without blocking)\r\n# This will report violations but not block content\r\nCSP_REPORT_ONLY = 'FALSE'\r\n\r\n# Default source directive - restricts all resource types by default\r\nCSP_DEFAULT_SRC = \"'self'\"\r\n\r\n# Script source directive - controls where scripts can be loaded from\r\n# Includes Socket.IO CDN which is required by the application\r\n# 'unsafe-inline' is needed for Socket.IO to function properly\r\n# Cloudflare Insights is used for analytics\r\nCSP_SCRIPT_SRC = \"'self' 'unsafe-inline' https://cdn.socket.io https://static.cloudflareinsights.com\"\r\n\r\n# Style source directive - controls where styles can be loaded from\r\n# 'unsafe-inline' is needed for some inline styles in the application\r\nCSP_STYLE_SRC = \"'self' 'unsafe-inline'\"\r\n\r\n# Image source directive - controls where images can be loaded from\r\n# 'data:' allows base64 encoded images\r\nCSP_IMG_SRC = \"'self' data:\"\r\n\r\n# Connect source directive - controls what network connections are allowed\r\n# Includes WebSocket connections needed for real-time updates\r\nCSP_CONNECT_SRC = \"'self' wss: ws:\"\r\n\r\n# Font source directive - controls where fonts can be loaded from\r\nCSP_FONT_SRC = \"'self'\"\r\n\r\n# Object source directive - controls where plugins can be loaded from\r\n# 'none' disables all object, embed, and applet elements\r\nCSP_OBJECT_SRC = \"'none'\"\r\n\r\n# Media source directive - controls where audio and video can be loaded from\r\n# Allows audio alerts from your domain and potentially CDN sources in the future\r\nCSP_MEDIA_SRC = \"'self' data: https://*.amazonaws.com https://*.cloudfront.net\"\r\n\r\n# Frame source directive - controls where iframes can be loaded from\r\n# If you integrate with TradingView or other platforms, you may need to add their domains\r\nCSP_FRAME_SRC = \"'self'\"\r\n\r\n# Form action directive - restricts where forms can be submitted to\r\nCSP_FORM_ACTION = \"'self'\"\r\n\r\n# Frame ancestors directive - controls which sites can embed your site in frames\r\n# This helps prevent clickjacking attacks\r\nCSP_FRAME_ANCESTORS = \"'self'\"\r\n\r\n# Base URI directive - restricts what base URIs can be used\r\nCSP_BASE_URI = \"'self'\"\r\n\r\n# Set to TRUE to upgrade insecure (HTTP) requests to HTTPS\r\n# Recommended for production environments\r\nCSP_UPGRADE_INSECURE_REQUESTS = 'FALSE'\r\n\r\n# URI to report CSP violations to (optional)\r\n# Example: /csp-report\r\nCSP_REPORT_URI = ''\r\n\r\n# CSRF (Cross-Site Request Forgery) Protection Configuration\r\n# Set to TRUE to enable CSRF protection, FALSE to disable\r\nCSRF_ENABLED = 'TRUE'\r\n\r\n# CSRF Token Time Limit (in seconds)\r\n# Leave empty for no time limit (tokens valid for entire session)\r\n# Example: 3600 = 1 hour, 86400 = 24 hours\r\nCSRF_TIME_LIMIT = ''\r\n", "path": "ubuntu/git/algofactory/.env", "language": "properties", "prefixBegin": 0, "suffixEnd": 0}}]]