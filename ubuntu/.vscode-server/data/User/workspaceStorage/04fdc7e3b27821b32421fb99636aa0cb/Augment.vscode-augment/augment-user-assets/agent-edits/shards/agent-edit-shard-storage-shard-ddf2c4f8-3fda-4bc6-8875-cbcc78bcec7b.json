{"id": "shard-ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "checkpoints": {"ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/run_dashboard.py": [{"sourceToolCallRequestId": "16eaed97-2df4-4856-ab14-55538ea0fc21", "timestamp": 0, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/run_dashboard.py"}}}, {"sourceToolCallRequestId": "2db0a3e4-d25b-4998-a7da-b7a6052feb74", "timestamp": 1750590889940, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/run_dashboard.py"}}}, {"sourceToolCallRequestId": "3c1aad07-51d6-46a7-b03c-b448ca9e7ba2", "timestamp": 1750590889998, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/run_dashboard.py"}}}, {"sourceToolCallRequestId": "40ccf03d-0285-4ac8-8d71-a19ca5d32d3e", "timestamp": 1750590889998, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/run_dashboard.py"}}}, {"sourceToolCallRequestId": "5fb102e3-7af1-43a1-a0cd-6e932f33ec5c", "timestamp": 1750590902231, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/run_dashboard.py"}}}, {"sourceToolCallRequestId": "2abbd5ed-63ae-408c-acd9-ab5ddd97ee35", "timestamp": 1750590902262, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/run_dashboard.py"}}}, {"sourceToolCallRequestId": "87e96cc9-e2b8-4da1-b486-46fe8b2abd30", "timestamp": 1750590902262, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "lastIncludedInRequestId": "07c84141-9fcf-4133-a998-0f633794a412", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/run_dashboard.py"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/8502.algofactory.in.conf": [{"sourceToolCallRequestId": "f945af59-52b0-4851-b808-4fe90f70faa4", "timestamp": 1750590931495, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/8502.algofactory.in.conf"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-nginx.sh": [{"sourceToolCallRequestId": "8cec35d2-163a-4890-b21b-e0840059f2a6", "timestamp": 1750590951452, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/setup-nginx.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-ssl.sh": [{"sourceToolCallRequestId": "e36e473e-d94c-463b-ad98-0c2ce8d774ef", "timestamp": 1750590993393, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/setup-ssl.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/algofactory-dashboard.service": [{"sourceToolCallRequestId": "d3a25aa6-9ba6-42b6-83cc-bef0cef3367e", "timestamp": 1750591022427, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/algofactory-dashboard.service"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-service.sh": [{"sourceToolCallRequestId": "b900a7a8-148a-46c5-9c12-6b9d0365b350", "timestamp": 1750591039426, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/setup-service.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-firewall.sh": [{"sourceToolCallRequestId": "c5287f06-a17c-416c-8f78-bd18b696bd83", "timestamp": 1750591086325, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/setup-firewall.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/DNS-SETUP-GUIDE.md": [{"sourceToolCallRequestId": "8d11c37d-8d75-4a08-9bcc-5bab7712b4e8", "timestamp": 1750591124078, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/DNS-SETUP-GUIDE.md"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-dashboard.sh": [{"sourceToolCallRequestId": "995d19ab-74d4-49f7-b749-173fa7b7289a", "timestamp": 1750591161451, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/deploy-dashboard.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/README.md": [{"sourceToolCallRequestId": "d1dd3ab0-d7c4-45ef-b49a-4b80e0a0372f", "timestamp": 1750591198063, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/README.md"}}}, {"sourceToolCallRequestId": "617f32bb-d643-48be-9d46-2bf03b2c9cfb", "timestamp": 1750591730694, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/README.md"}}}, {"sourceToolCallRequestId": "a365b3ca-4d0b-4ce6-a33f-28a608c2c837", "timestamp": 1750591730743, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/README.md"}}}, {"sourceToolCallRequestId": "309bf352-1eb1-464a-b70a-b144452d0acd", "timestamp": 1750591730743, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "lastIncludedInRequestId": "285cc54f-7678-4f61-ad72-a3cff28e01fb", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/README.md"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/auth_manager.py": [{"sourceToolCallRequestId": "2b84901a-a90a-47dd-821f-d78558605e9a", "timestamp": 1750591369630, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/auth_manager.py"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/user_manager.py": [{"sourceToolCallRequestId": "4ed8f374-805c-41d2-8181-cd4ed5d5f564", "timestamp": 1750591421231, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/user_manager.py"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/auth_config.py": [{"sourceToolCallRequestId": "b3705ce0-b2b2-470d-93c6-1f78626bccd4", "timestamp": 1750591461860, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/auth_config.py"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/8502.algofactory.in-secure.conf": [{"sourceToolCallRequestId": "c1f5b5e4-ba16-4d38-9031-7e87c405ecd4", "timestamp": 1750591497114, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/8502.algofactory.in-secure.conf"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-auth.sh": [{"sourceToolCallRequestId": "e754e65a-6f95-4308-b300-ac4e78ca0713", "timestamp": 1750591546295, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/setup-auth.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/simple_dashboard.py": [{"sourceToolCallRequestId": "5005fa6d-c613-47c8-8ad6-6c73bc144ca6", "timestamp": 0, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/simple_dashboard.py"}}}, {"sourceToolCallRequestId": "11f28a5b-60c3-4f74-9034-0c6a62a541e1", "timestamp": 1750591577193, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/simple_dashboard.py"}}}, {"sourceToolCallRequestId": "d3e31291-ff41-40f8-8223-2a41e974cf7e", "timestamp": 1750591577387, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/simple_dashboard.py"}}}, {"sourceToolCallRequestId": "e4ca6c65-238e-452d-9ed5-bd7ceae3d171", "timestamp": 1750591577387, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/simple_dashboard.py"}}}, {"sourceToolCallRequestId": "01f23137-31db-492b-a40d-75dcbb09d2df", "timestamp": 1750591620981, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/simple_dashboard.py"}}}, {"sourceToolCallRequestId": "d305413e-5a99-46a9-a0f1-517758a8dd2a", "timestamp": 1750591621045, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/simple_dashboard.py"}}}, {"sourceToolCallRequestId": "11a6a45b-af7f-4322-bff7-0988a0069b81", "timestamp": 1750591621045, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "lastIncludedInRequestId": "285cc54f-7678-4f61-ad72-a3cff28e01fb", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/simple_dashboard.py"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-secure-dashboard.sh": [{"sourceToolCallRequestId": "d888a6f7-4a7a-44bc-a614-8a749f58c3e0", "timestamp": 1750591673362, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/deploy-secure-dashboard.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/SECURITY-GUIDE.md": [{"sourceToolCallRequestId": "42d545ad-4e2e-46d7-960f-92ebb21234bd", "timestamp": 1750591716250, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/SECURITY-GUIDE.md"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/HOSTINGER-DNS-SETUP.md": [{"sourceToolCallRequestId": "af817650-3539-41da-8794-48f381842ae8", "timestamp": 1750592176857, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/HOSTINGER-DNS-SETUP.md"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/dashboard.algofactory.in.conf": [{"sourceToolCallRequestId": "95980fe6-811f-4226-8b9a-31659b0a6493", "timestamp": 1750592209212, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "e313fd9a-de2e-4efe-b0ea-10fb091b2955", "timestamp": 1750593379682, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "d33d53d7-6a8a-45d6-94f6-519881fe6e07", "timestamp": 1750593379771, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "fe8fea72-f64b-4c7c-97bd-0c24c78ba633", "timestamp": 1750593379771, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "a1572660-3e7b-443b-b028-6a0a69b71ca7", "timestamp": 1750593400563, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "a3de7eb6-f764-4f1f-a7ea-9db9ccd55a9d", "timestamp": 1750593400614, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "14419653-6654-4d67-b9ed-e89761dd96ff", "timestamp": 1750593400614, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "38344fcf-85f2-4c67-b0eb-1409fb447901", "timestamp": 1750593412525, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "2cff4330-eef5-44a4-8c62-0c1f14e7c139", "timestamp": 1750593412617, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "061049fa-beb0-4a35-8f40-b9e319b0f346", "timestamp": 1750593412617, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "lastIncludedInRequestId": "d5ff1210-bbaf-4cb1-84b7-349ab6363f1d", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "69696655-2ece-496c-977a-07bf82686396", "timestamp": 1750595606157, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "cf783f4b-d0cd-4d14-87f8-31cd61bfd6c8", "timestamp": 1750595606387, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "fde2a7f7-1381-4e26-9de2-7318ba55d3b1", "timestamp": 1750595606392, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "065cd5eb-5e41-47d5-9e27-3a7b4877d77c", "timestamp": 1750595618354, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "747e3058-dc17-47a8-bfb5-c09bc3923688", "timestamp": 1750595618431, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "f939d2c0-088d-41f5-9ab2-739a7bb0449d", "timestamp": 1750595618431, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "a6ce16bc-85b8-4a9b-bb8b-85500a0e48c8", "timestamp": 1750595630018, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "7c198245-f722-4c5a-8d96-62128bbc18a6", "timestamp": 1750595630067, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "3d177542-8735-41c6-85be-801dbed79c41", "timestamp": 1750595630067, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "lastIncludedInRequestId": "ff89425d-3318-4150-a655-cc8984df68e9", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "25f62009-815d-4b95-abfc-7601e3bd32cd", "timestamp": 1750596591505, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "555188fb-d1d6-4355-886a-0b5fa34f59fa", "timestamp": 1750596591742, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "383a825d-cd4c-45a7-93d0-002d3126e737", "timestamp": 1750596591742, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "f2db4f98-8dab-4ad7-9c89-5187bb89a5ff", "timestamp": 1750596604840, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "572345fc-e26b-4635-adef-912948084e9a", "timestamp": 1750596604864, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}, {"sourceToolCallRequestId": "436cdc9e-d224-44c6-be3e-4dea80543685", "timestamp": 1750596604864, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "lastIncludedInRequestId": "51a9dde2-5100-491b-8f6f-0a68056e77f1", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/dashboard.algofactory.in.conf"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/client-template.algofactory.in.conf": [{"sourceToolCallRequestId": "93aea84b-65a4-4b25-9e8e-5fb00a29c581", "timestamp": 1750592240751, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/client-template.algofactory.in.conf"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-subdomains.sh": [{"sourceToolCallRequestId": "216fadfd-8ea1-4d5a-91fe-ba7d12e9064b", "timestamp": 1750592267158, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/setup-subdomains.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-ssl-subdomains.sh": [{"sourceToolCallRequestId": "0ecef250-05cf-4551-94e6-203bbac2e36b", "timestamp": 1750592339805, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/setup-ssl-subdomains.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-new-client.sh": [{"sourceToolCallRequestId": "8e98e744-c9e3-4b01-a3d5-ca2518c5bfd3", "timestamp": 1750592396107, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/deploy-new-client.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/manage-clients.sh": [{"sourceToolCallRequestId": "d90401ac-2abe-4742-9843-97f303893b61", "timestamp": 1750592449560, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/manage-clients.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-complete-system.sh": [{"sourceToolCallRequestId": "5ee3ac9b-5874-4b93-930b-32ee9d4ae116", "timestamp": 1750592510262, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/deploy-complete-system.sh"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/COMPLETE-SETUP-GUIDE.md": [{"sourceToolCallRequestId": "fcd1a532-6db4-4372-890e-6f5fdee39f3c", "timestamp": 1750592556987, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/COMPLETE-SETUP-GUIDE.md"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/algofactory/.env": [{"sourceToolCallRequestId": "cf406f39-2df2-43d1-bfa4-6fc5ce2f8fb9", "timestamp": 0, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 0, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "25906d3f-f029-4586-bd25-5c221b84372c", "timestamp": 1750597924220, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "f8c6eb2f-6eaa-4c4e-af23-ef6efcefbbba", "timestamp": 1750597924358, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "6054d338-4973-4f52-818c-51c2d8fc46de", "timestamp": 1750597924358, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "6dfc8004-bacd-4556-aedc-d2252bcba5e1", "timestamp": 1750597935717, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "dc4a74ec-3053-4f02-845b-649dc65d4087", "timestamp": 1750597935790, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "478a18c8-0def-4c75-9992-614e8d675071", "timestamp": 1750597935790, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "b7f96446-b4d1-404d-855c-a83656203322", "timestamp": 1750597947227, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "70d7a21a-38f9-4d63-b706-0d94382928a3", "timestamp": 1750597947287, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "4a927e02-1cba-40b4-a36a-2953d9b0ffba", "timestamp": 1750597947287, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "c13fb561-7cb1-484c-aeba-0488438b15ca", "timestamp": 1750597958009, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "91007465-67a4-4994-ac9f-bf5b02f0a0d1", "timestamp": 1750597958032, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}, {"sourceToolCallRequestId": "194556f1-bdb3-4293-bc97-ec7931e2eb74", "timestamp": 1750597958032, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "lastIncludedInRequestId": "0c5cacf6-ecab-4b2e-a41f-27b64cd68b16", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/algofactory/.env"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/5000.algofactory.in.conf": [{"sourceToolCallRequestId": "a3019502-9a2c-4124-989a-41bf57b86c2c", "timestamp": 1750598938380, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in.conf"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/backup.algofactory.in.conf": [{"sourceToolCallRequestId": "fe895384-71f2-4959-ba5b-71b507b04fb3", "timestamp": 1750601300973, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/backup.algofactory.in.conf"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/algofactory-dashboard.service": [{"sourceToolCallRequestId": "bb1225e9-e114-4c60-878a-da22dd368e4d", "timestamp": 1750602986700, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "tmp/algofactory-dashboard.service"}}}], "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/5000.algofactory.in-http.conf": [{"sourceToolCallRequestId": "d1c568bc-0146-48a8-8164-8490a3da3346", "timestamp": 1750603445298, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}}}, {"sourceToolCallRequestId": "f9867019-65e5-49b2-97de-11be7812af28", "timestamp": 1750603830390, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}}}, {"sourceToolCallRequestId": "ff9adfd4-330d-48aa-8a2c-b0343f662b36", "timestamp": 1750603830766, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}}}, {"sourceToolCallRequestId": "d5c66278-33f8-4247-a614-aeeee6c4e6ab", "timestamp": 1750603830766, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "lastIncludedInRequestId": "1a07cd2a-bd6b-4693-a76a-3bab892f8bbf", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}}}, {"sourceToolCallRequestId": "df629674-1c59-4452-8e6f-260bb269e8a7", "timestamp": 1750604033871, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}}}, {"sourceToolCallRequestId": "3911b572-51d6-47dc-bc47-1e2654c1a454", "timestamp": 1750604033977, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}}}, {"sourceToolCallRequestId": "86f89ae5-5633-49ac-8384-300567d88087", "timestamp": 1750604033978, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "lastIncludedInRequestId": "17f282c7-3836-46d8-83e0-c7261a76a25b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}}}, {"sourceToolCallRequestId": "ae926c94-26c0-4465-9d52-fbe536256ece", "timestamp": 1750604401241, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}}}, {"sourceToolCallRequestId": "04ef4c4e-652e-4226-81dd-2c06c4978af3", "timestamp": 1750604401354, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}}}, {"sourceToolCallRequestId": "e61d21d0-ff1f-4699-a1b6-493cac9c3158", "timestamp": 1750604401354, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}}}, {"sourceToolCallRequestId": "89ef3ec5-a28e-4869-a3a8-d27dd6c03cc0", "timestamp": 1750604415307, "conversationId": "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b", "editSource": 1, "lastIncludedInRequestId": "7775c700-5789-4609-a599-d7b070db657e", "documentMetadata": {"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}}}]}, "metadata": {"checkpointDocumentIds": ["ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/run_dashboard.py", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/8502.algofactory.in.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-nginx.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-ssl.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/algofactory-dashboard.service", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-service.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-firewall.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/DNS-SETUP-GUIDE.md", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-dashboard.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/README.md", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/auth_manager.py", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/user_manager.py", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/auth_config.py", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/8502.algofactory.in-secure.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-auth.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/simple_dashboard.py", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-secure-dashboard.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/SECURITY-GUIDE.md", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/HOSTINGER-DNS-SETUP.md", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/dashboard.algofactory.in.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/client-template.algofactory.in.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-subdomains.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-ssl-subdomains.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-new-client.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/manage-clients.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-complete-system.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/COMPLETE-SETUP-GUIDE.md", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/algofactory/.env", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/5000.algofactory.in.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/backup.algofactory.in.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/algofactory-dashboard.service", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/5000.algofactory.in-http.conf"], "size": 2431125, "checkpointCount": 93, "lastModified": 1750605688760}}