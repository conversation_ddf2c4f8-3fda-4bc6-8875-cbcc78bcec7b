{"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/enhanced_dashboard.py"}, "originalCode": "#!/usr/bin/env python3\n\"\"\"\nEnhanced AlgoFactory Dashboard with Automated Instance Management\nSingle-click instance creation, management, nginx, and SSL automation.\n\"\"\"\n\nimport streamlit as st\nimport pandas as pd\nimport time\nfrom automated_instance_manager import AutomatedInstanceManager\nfrom auth_config import auth_config\nimport plotly.express as px\nimport plotly.graph_objects as go\n\n# Page configuration\nst.set_page_config(\n    page_title=\"AlgoFactory Control Center\",\n    page_icon=\"🚀\",\n    layout=\"wide\",\n    initial_sidebar_state=\"expanded\"\n)\n\n# Authentication check\nif not auth_config.is_auth_enabled() or auth_config.check_authentication():\n    \n    # Initialize manager\n    if 'manager' not in st.session_state:\n        st.session_state.manager = AutomatedInstanceManager()\n    \n    manager = st.session_state.manager\n    \n    # Header\n    st.title(\"🚀 AlgoFactory Control Center\")\n    st.markdown(\"**Automated Instance Management with Nginx & SSL**\")\n    \n    # Sidebar\n    with st.sidebar:\n        st.header(\"🎛️ Control Panel\")\n        \n        # System Status\n        st.subheader(\"📊 System Status\")\n        status = manager.get_system_status()\n        \n        col1, col2 = st.columns(2)\n        with col1:\n            st.metric(\"Total Instances\", status[\"total_instances\"])\n            st.metric(\"SSL Certificates\", status[\"ssl_certificates\"])\n        with col2:\n            st.metric(\"Running\", status[\"running_instances\"])\n            st.metric(\"Available Ports\", status[\"available_ports\"])\n        \n        st.markdown(f\"**Nginx:** {status['nginx_status']}\")\n        \n        # Quick Actions\n        st.subheader(\"⚡ Quick Actions\")\n        \n        if st.button(\"🔄 Refresh Data\", use_container_width=True):\n            st.rerun()\n        \n        if st.button(\"🛑 Stop All Instances\", use_container_width=True):\n            with st.spinner(\"Stopping all instances...\"):\n                instances = manager.get_instances()\n                for instance in instances:\n                    if \"Running\" in instance[\"Status\"]:\n                        manager.stop_instance(instance[\"Port\"])\n            st.success(\"All instances stopped!\")\n            st.rerun()\n    \n    # Main content tabs\n    tab1, tab2, tab3, tab4 = st.tabs([\"📋 Instance Manager\", \"➕ Create Instance\", \"📊 Monitoring\", \"⚙️ Settings\"])\n    \n    with tab1:\n        st.header(\"📋 Instance Manager\")\n        \n        # Get instances\n        instances = manager.get_instances()\n        \n        if instances:\n            # Create DataFrame\n            df = pd.DataFrame(instances)\n            \n            # Display instances with action buttons\n            for idx, instance in enumerate(instances):\n                with st.container():\n                    col1, col2, col3, col4, col5, col6, col7 = st.columns([1, 2, 1, 1, 1, 1, 1])\n                    \n                    with col1:\n                        st.write(f\"**{instance['Port']}**\")\n                    \n                    with col2:\n                        st.write(f\"[{instance['Name']}]({instance['URL']})\")\n                        st.caption(f\"{instance['Domain']}\")\n                    \n                    with col3:\n                        st.write(instance['Status'])\n                    \n                    with col4:\n                        if \"Running\" in instance['Status']:\n                            if st.button(\"🛑 Stop\", key=f\"stop_{instance['Port']}\"):\n                                with st.spinner(f\"Stopping port {instance['Port']}...\"):\n                                    success = manager.stop_instance(instance['Port'])\n                                if success:\n                                    st.success(f\"Stopped port {instance['Port']}\")\n                                else:\n                                    st.error(f\"Failed to stop port {instance['Port']}\")\n                                st.rerun()\n                        else:\n                            if st.button(\"▶️ Start\", key=f\"start_{instance['Port']}\"):\n                                with st.spinner(f\"Starting port {instance['Port']}...\"):\n                                    success = manager.start_instance(instance['Port'])\n                                if success:\n                                    st.success(f\"Started port {instance['Port']}\")\n                                else:\n                                    st.error(f\"Failed to start port {instance['Port']}\")\n                                st.rerun()\n                    \n                    with col5:\n                        if st.button(\"🔄 Restart\", key=f\"restart_{instance['Port']}\"):\n                            with st.spinner(f\"Restarting port {instance['Port']}...\"):\n                                success = manager.restart_instance(instance['Port'])\n                            if success:\n                                st.success(f\"Restarted port {instance['Port']}\")\n                            else:\n                                st.error(f\"Failed to restart port {instance['Port']}\")\n                            st.rerun()\n                    \n                    with col6:\n                        if st.button(\"🌐 Open\", key=f\"open_{instance['Port']}\"):\n                            st.markdown(f\"[Open {instance['Domain']}]({instance['URL']})\")\n                    \n                    with col7:\n                        if st.button(\"🗑️ Delete\", key=f\"delete_{instance['Port']}\"):\n                            if st.session_state.get(f\"confirm_delete_{instance['Port']}\", False):\n                                with st.spinner(f\"Deleting instance {instance['Port']}...\"):\n                                    success = manager.delete_instance(instance['Port'])\n                                if success:\n                                    st.success(f\"Deleted instance {instance['Port']}\")\n                                else:\n                                    st.error(f\"Failed to delete instance {instance['Port']}\")\n                                st.session_state[f\"confirm_delete_{instance['Port']}\"] = False\n                                st.rerun()\n                            else:\n                                st.session_state[f\"confirm_delete_{instance['Port']}\"] = True\n                                st.warning(\"Click again to confirm deletion\")\n                    \n                    st.divider()\n        else:\n            st.info(\"No instances created yet. Use the 'Create Instance' tab to get started!\")\n    \n    with tab2:\n        st.header(\"➕ Create New Instance\")\n        \n        col1, col2 = st.columns([2, 1])\n        \n        with col1:\n            st.subheader(\"🔧 Instance Configuration\")\n            \n            # Port selection\n            available_ports = manager.get_available_ports()\n            if available_ports:\n                selected_port = st.selectbox(\n                    \"Select Port\",\n                    available_ports,\n                    help=\"Choose from available ports (1010-1020)\"\n                )\n            else:\n                st.error(\"No available ports in range 1010-1020\")\n                selected_port = None\n            \n            # Instance name\n            instance_name = st.text_input(\n                \"Instance Name\",\n                value=f\"algofactory-{selected_port}\" if selected_port else \"\",\n                help=\"Custom name for this instance\"\n            )\n            \n            # Auto-start option\n            auto_start = st.checkbox(\n                \"Auto-start after creation\",\n                value=True,\n                help=\"Automatically start the instance after creation\"\n            )\n            \n            # Advanced options\n            with st.expander(\"🔧 Advanced Options\"):\n                auto_nginx = st.checkbox(\"Auto-configure Nginx\", value=True)\n                auto_ssl = st.checkbox(\"Auto-setup SSL Certificate\", value=True)\n                \n                if auto_ssl and not auto_nginx:\n                    st.warning(\"SSL requires Nginx configuration\")\n        \n        with col2:\n            st.subheader(\"📋 Creation Summary\")\n            if selected_port:\n                domain = f\"{selected_port}.algofactory.in\"\n                st.write(f\"**Port:** {selected_port}\")\n                st.write(f\"**Domain:** {domain}\")\n                st.write(f\"**URL:** https://{domain}\")\n                st.write(f\"**Auto-start:** {'Yes' if auto_start else 'No'}\")\n                st.write(f\"**Nginx:** {'Yes' if auto_nginx else 'No'}\")\n                st.write(f\"**SSL:** {'Yes' if auto_ssl else 'No'}\")\n        \n        # Create button\n        if selected_port and instance_name:\n            if st.button(\"🚀 Create Instance\", type=\"primary\", use_container_width=True):\n                # Update manager settings\n                manager.config[\"settings\"][\"auto_nginx\"] = auto_nginx\n                manager.config[\"settings\"][\"auto_ssl\"] = auto_ssl\n                manager.save_config()\n                \n                with st.spinner(f\"Creating instance {instance_name} on port {selected_port}...\"):\n                    progress_bar = st.progress(0)\n                    status_text = st.empty()\n                    \n                    # Simulate progress updates\n                    status_text.text(\"Creating instance directory...\")\n                    progress_bar.progress(20)\n                    time.sleep(1)\n                    \n                    status_text.text(\"Configuring environment...\")\n                    progress_bar.progress(40)\n                    \n                    if auto_nginx:\n                        status_text.text(\"Setting up Nginx configuration...\")\n                        progress_bar.progress(60)\n                        time.sleep(1)\n                    \n                    if auto_ssl:\n                        status_text.text(\"Obtaining SSL certificate...\")\n                        progress_bar.progress(80)\n                        time.sleep(2)\n                    \n                    status_text.text(\"Finalizing setup...\")\n                    progress_bar.progress(90)\n                    \n                    # Actually create the instance\n                    success = manager.create_instance(\n                        selected_port, \n                        instance_name, \n                        auto_start=auto_start\n                    )\n                    \n                    progress_bar.progress(100)\n                    \n                    if success:\n                        st.success(f\"✅ Successfully created instance {instance_name}!\")\n                        if auto_start:\n                            st.info(f\"🌐 Access your instance at: https://{domain}\")\n                        time.sleep(2)\n                        st.rerun()\n                    else:\n                        st.error(f\"❌ Failed to create instance {instance_name}\")\n        \n        # Bulk creation\n        st.subheader(\"📦 Bulk Creation\")\n        col1, col2 = st.columns(2)\n        \n        with col1:\n            bulk_ports = st.multiselect(\n                \"Select Multiple Ports\",\n                available_ports,\n                help=\"Create multiple instances at once\"\n            )\n        \n        with col2:\n            bulk_auto_start = st.checkbox(\"Auto-start all\", value=False)\n        \n        if bulk_ports:\n            if st.button(\"🚀 Create All Selected\", type=\"secondary\"):\n                with st.spinner(f\"Creating {len(bulk_ports)} instances...\"):\n                    results = manager.bulk_create_instances(bulk_ports, auto_start=bulk_auto_start)\n                    \n                    success_count = sum(1 for r in results if r[\"success\"])\n                    st.success(f\"Created {success_count}/{len(bulk_ports)} instances successfully!\")\n                    \n                    for result in results:\n                        if not result[\"success\"]:\n                            st.error(f\"Failed to create instance on port {result['port']}\")\n                \n                st.rerun()\n    \n    with tab3:\n        st.header(\"📊 System Monitoring\")\n        \n        # Get instances for monitoring\n        instances = manager.get_instances()\n        \n        if instances:\n            # Status distribution\n            col1, col2 = st.columns(2)\n            \n            with col1:\n                status_counts = {}\n                for instance in instances:\n                    status = instance['Status'].split()[1] if len(instance['Status'].split()) > 1 else instance['Status']\n                    status_counts[status] = status_counts.get(status, 0) + 1\n                \n                fig_pie = px.pie(\n                    values=list(status_counts.values()),\n                    names=list(status_counts.keys()),\n                    title=\"Instance Status Distribution\"\n                )\n                st.plotly_chart(fig_pie, use_container_width=True)\n            \n            with col2:\n                # Port usage chart\n                ports = [instance['Port'] for instance in instances]\n                statuses = [instance['Status'] for instance in instances]\n                \n                colors = ['green' if 'Running' in status else 'red' for status in statuses]\n                \n                fig_bar = go.Figure(data=[\n                    go.Bar(x=ports, y=[1]*len(ports), marker_color=colors)\n                ])\n                fig_bar.update_layout(\n                    title=\"Port Usage Status\",\n                    xaxis_title=\"Port Number\",\n                    yaxis_title=\"Status\",\n                    showlegend=False\n                )\n                st.plotly_chart(fig_bar, use_container_width=True)\n            \n            # Instance details table\n            st.subheader(\"📋 Detailed Instance Information\")\n            df = pd.DataFrame(instances)\n            st.dataframe(df, use_container_width=True)\n        \n        else:\n            st.info(\"No instances to monitor. Create some instances first!\")\n    \n    with tab4:\n        st.header(\"⚙️ System Settings\")\n        \n        col1, col2 = st.columns(2)\n        \n        with col1:\n            st.subheader(\"🔧 Default Settings\")\n            \n            current_auto_nginx = manager.config[\"settings\"].get(\"auto_nginx\", True)\n            current_auto_ssl = manager.config[\"settings\"].get(\"auto_ssl\", True)\n            \n            new_auto_nginx = st.checkbox(\"Auto-configure Nginx by default\", value=current_auto_nginx)\n            new_auto_ssl = st.checkbox(\"Auto-setup SSL by default\", value=current_auto_ssl)\n            \n            if st.button(\"💾 Save Settings\"):\n                manager.config[\"settings\"][\"auto_nginx\"] = new_auto_nginx\n                manager.config[\"settings\"][\"auto_ssl\"] = new_auto_ssl\n                manager.save_config()\n                st.success(\"Settings saved!\")\n        \n        with col2:\n            st.subheader(\"🧹 Maintenance\")\n            \n            if st.button(\"🔄 Reload Nginx\"):\n                try:\n                    import subprocess\n                    subprocess.run([\"sudo\", \"systemctl\", \"reload\", \"nginx\"], check=True)\n                    st.success(\"Nginx reloaded successfully!\")\n                except:\n                    st.error(\"Failed to reload Nginx\")\n            \n            if st.button(\"📋 Show SSL Certificates\"):\n                try:\n                    import subprocess\n                    result = subprocess.run([\"sudo\", \"certbot\", \"certificates\"], \n                                          capture_output=True, text=True)\n                    st.code(result.stdout)\n                except:\n                    st.error(\"Failed to get SSL certificate information\")\n            \n            if st.button(\"🧹 Clean Up Unused Configs\"):\n                st.info(\"This feature will be implemented in the next update\")\n\nelse:\n    st.error(\"Authentication required. Please log in to access the dashboard.\")\n", "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nEnhanced AlgoFactory Dashboard with Automated Instance Management\nSingle-click instance creation, management, nginx, and SSL automation.\n\"\"\"\n\nimport streamlit as st\nimport pandas as pd\nimport time\nfrom automated_instance_manager import AutomatedInstanceManager\nfrom auth_config import auth_config\nimport plotly.express as px\nimport plotly.graph_objects as go\n\n# Page configuration\nst.set_page_config(\n    page_title=\"AlgoFactory Control Center\",\n    page_icon=\"🚀\",\n    layout=\"wide\",\n    initial_sidebar_state=\"expanded\"\n)\n\n# Authentication check\nif not auth_config.is_auth_enabled() or auth_config.check_authentication():\n    \n    # Initialize manager\n    if 'manager' not in st.session_state:\n        st.session_state.manager = AutomatedInstanceManager()\n    \n    manager = st.session_state.manager\n    \n    # Header\n    st.title(\"🚀 AlgoFactory Control Center\")\n    st.markdown(\"**Automated Instance Management with Nginx & SSL**\")\n    \n    # Sidebar\n    with st.sidebar:\n        st.header(\"🎛️ Control Panel\")\n        \n        # System Status\n        st.subheader(\"📊 System Status\")\n        status = manager.get_system_status()\n        \n        col1, col2 = st.columns(2)\n        with col1:\n            st.metric(\"Total Instances\", status[\"total_instances\"])\n            st.metric(\"SSL Certificates\", status[\"ssl_certificates\"])\n        with col2:\n            st.metric(\"Running\", status[\"running_instances\"])\n            st.metric(\"Available Ports\", status[\"available_ports\"])\n        \n        st.markdown(f\"**Nginx:** {status['nginx_status']}\")\n        \n        # Quick Actions\n        st.subheader(\"⚡ Quick Actions\")\n\n        if st.button(\"🔄 Refresh Data\", use_container_width=True):\n            st.rerun()\n\n        if st.button(\"🚀 Start All Instances\", use_container_width=True):\n            with st.spinner(\"Starting all instances...\"):\n                instances = manager.get_instances()\n                success_count = 0\n                for instance in instances:\n                    if \"Stopped\" in instance[\"Status\"]:\n                        if manager.start_instance(instance[\"Port\"]):\n                            success_count += 1\n                st.success(f\"Started {success_count} instances!\")\n            st.rerun()\n\n        if st.button(\"🛑 Stop All Instances\", use_container_width=True):\n            with st.spinner(\"Stopping all instances...\"):\n                instances = manager.get_instances()\n                success_count = 0\n                for instance in instances:\n                    if \"Running\" in instance[\"Status\"]:\n                        if manager.stop_instance(instance[\"Port\"]):\n                            success_count += 1\n                st.success(f\"Stopped {success_count} instances!\")\n            st.rerun()\n    \n    # Main content tabs\n    tab1, tab2, tab3, tab4 = st.tabs([\"📋 Instance Manager\", \"➕ Create Instance\", \"📊 Monitoring\", \"⚙️ Settings\"])\n    \n    with tab1:\n        st.header(\"📋 Instance Manager\")\n        \n        # Get instances\n        instances = manager.get_instances()\n        \n        if instances:\n            # Create DataFrame\n            df = pd.DataFrame(instances)\n            \n            # Display instances with action buttons\n            for idx, instance in enumerate(instances):\n                with st.container():\n                    col1, col2, col3, col4, col5, col6, col7 = st.columns([1, 2, 1, 1, 1, 1, 1])\n                    \n                    with col1:\n                        st.write(f\"**{instance['Port']}**\")\n                    \n                    with col2:\n                        st.write(f\"[{instance['Name']}]({instance['URL']})\")\n                        st.caption(f\"{instance['Domain']}\")\n                    \n                    with col3:\n                        st.write(instance['Status'])\n                    \n                    with col4:\n                        if \"Running\" in instance['Status']:\n                            if st.button(\"🛑 Stop\", key=f\"stop_{instance['Port']}\"):\n                                with st.spinner(f\"Stopping port {instance['Port']}...\"):\n                                    success = manager.stop_instance(instance['Port'])\n                                if success:\n                                    st.success(f\"Stopped port {instance['Port']}\")\n                                else:\n                                    st.error(f\"Failed to stop port {instance['Port']}\")\n                                st.rerun()\n                        else:\n                            if st.button(\"▶️ Start\", key=f\"start_{instance['Port']}\"):\n                                with st.spinner(f\"Starting port {instance['Port']}...\"):\n                                    success = manager.start_instance(instance['Port'])\n                                if success:\n                                    st.success(f\"Started port {instance['Port']}\")\n                                else:\n                                    st.error(f\"Failed to start port {instance['Port']}\")\n                                st.rerun()\n                    \n                    with col5:\n                        if st.button(\"🔄 Restart\", key=f\"restart_{instance['Port']}\"):\n                            with st.spinner(f\"Restarting port {instance['Port']}...\"):\n                                success = manager.restart_instance(instance['Port'])\n                            if success:\n                                st.success(f\"Restarted port {instance['Port']}\")\n                            else:\n                                st.error(f\"Failed to restart port {instance['Port']}\")\n                            st.rerun()\n                    \n                    with col6:\n                        if st.button(\"🌐 Open\", key=f\"open_{instance['Port']}\"):\n                            st.markdown(f\"[Open {instance['Domain']}]({instance['URL']})\")\n                    \n                    with col7:\n                        if st.button(\"🗑️ Delete\", key=f\"delete_{instance['Port']}\"):\n                            if st.session_state.get(f\"confirm_delete_{instance['Port']}\", False):\n                                with st.spinner(f\"Deleting instance {instance['Port']}...\"):\n                                    success = manager.delete_instance(instance['Port'])\n                                if success:\n                                    st.success(f\"Deleted instance {instance['Port']}\")\n                                else:\n                                    st.error(f\"Failed to delete instance {instance['Port']}\")\n                                st.session_state[f\"confirm_delete_{instance['Port']}\"] = False\n                                st.rerun()\n                            else:\n                                st.session_state[f\"confirm_delete_{instance['Port']}\"] = True\n                                st.warning(\"Click again to confirm deletion\")\n                    \n                    st.divider()\n        else:\n            st.info(\"No instances created yet. Use the 'Create Instance' tab to get started!\")\n    \n    with tab2:\n        st.header(\"➕ Create New Instance\")\n        \n        col1, col2 = st.columns([2, 1])\n        \n        with col1:\n            st.subheader(\"🔧 Instance Configuration\")\n            \n            # Port selection\n            available_ports = manager.get_available_ports()\n            if available_ports:\n                selected_port = st.selectbox(\n                    \"Select Port\",\n                    available_ports,\n                    help=\"Choose from available ports (1010-1020)\"\n                )\n            else:\n                st.error(\"No available ports in range 1010-1020\")\n                selected_port = None\n            \n            # Instance name\n            instance_name = st.text_input(\n                \"Instance Name\",\n                value=f\"algofactory-{selected_port}\" if selected_port else \"\",\n                help=\"Custom name for this instance\"\n            )\n            \n            # Auto-start option\n            auto_start = st.checkbox(\n                \"Auto-start after creation\",\n                value=True,\n                help=\"Automatically start the instance after creation\"\n            )\n            \n            # Advanced options\n            with st.expander(\"🔧 Advanced Options\"):\n                auto_nginx = st.checkbox(\"Auto-configure Nginx\", value=True)\n                auto_ssl = st.checkbox(\"Auto-setup SSL Certificate\", value=True)\n                \n                if auto_ssl and not auto_nginx:\n                    st.warning(\"SSL requires Nginx configuration\")\n        \n        with col2:\n            st.subheader(\"📋 Creation Summary\")\n            if selected_port:\n                domain = f\"{selected_port}.algofactory.in\"\n                st.write(f\"**Port:** {selected_port}\")\n                st.write(f\"**Domain:** {domain}\")\n                st.write(f\"**URL:** https://{domain}\")\n                st.write(f\"**Auto-start:** {'Yes' if auto_start else 'No'}\")\n                st.write(f\"**Nginx:** {'Yes' if auto_nginx else 'No'}\")\n                st.write(f\"**SSL:** {'Yes' if auto_ssl else 'No'}\")\n        \n        # Create button\n        if selected_port and instance_name:\n            if st.button(\"🚀 Create Instance\", type=\"primary\", use_container_width=True):\n                # Update manager settings\n                manager.config[\"settings\"][\"auto_nginx\"] = auto_nginx\n                manager.config[\"settings\"][\"auto_ssl\"] = auto_ssl\n                manager.save_config()\n                \n                with st.spinner(f\"Creating instance {instance_name} on port {selected_port}...\"):\n                    progress_bar = st.progress(0)\n                    status_text = st.empty()\n                    \n                    # Simulate progress updates\n                    status_text.text(\"Creating instance directory...\")\n                    progress_bar.progress(20)\n                    time.sleep(1)\n                    \n                    status_text.text(\"Configuring environment...\")\n                    progress_bar.progress(40)\n                    \n                    if auto_nginx:\n                        status_text.text(\"Setting up Nginx configuration...\")\n                        progress_bar.progress(60)\n                        time.sleep(1)\n                    \n                    if auto_ssl:\n                        status_text.text(\"Obtaining SSL certificate...\")\n                        progress_bar.progress(80)\n                        time.sleep(2)\n                    \n                    status_text.text(\"Finalizing setup...\")\n                    progress_bar.progress(90)\n                    \n                    # Actually create the instance\n                    success = manager.create_instance(\n                        selected_port, \n                        instance_name, \n                        auto_start=auto_start\n                    )\n                    \n                    progress_bar.progress(100)\n                    \n                    if success:\n                        st.success(f\"✅ Successfully created instance {instance_name}!\")\n                        if auto_start:\n                            st.info(f\"🌐 Access your instance at: https://{domain}\")\n                        time.sleep(2)\n                        st.rerun()\n                    else:\n                        st.error(f\"❌ Failed to create instance {instance_name}\")\n        \n        # Bulk creation\n        st.subheader(\"📦 Bulk Creation\")\n        col1, col2 = st.columns(2)\n        \n        with col1:\n            bulk_ports = st.multiselect(\n                \"Select Multiple Ports\",\n                available_ports,\n                help=\"Create multiple instances at once\"\n            )\n        \n        with col2:\n            bulk_auto_start = st.checkbox(\"Auto-start all\", value=False)\n        \n        if bulk_ports:\n            if st.button(\"🚀 Create All Selected\", type=\"secondary\"):\n                with st.spinner(f\"Creating {len(bulk_ports)} instances...\"):\n                    results = manager.bulk_create_instances(bulk_ports, auto_start=bulk_auto_start)\n                    \n                    success_count = sum(1 for r in results if r[\"success\"])\n                    st.success(f\"Created {success_count}/{len(bulk_ports)} instances successfully!\")\n                    \n                    for result in results:\n                        if not result[\"success\"]:\n                            st.error(f\"Failed to create instance on port {result['port']}\")\n                \n                st.rerun()\n    \n    with tab3:\n        st.header(\"📊 System Monitoring\")\n        \n        # Get instances for monitoring\n        instances = manager.get_instances()\n        \n        if instances:\n            # Status distribution\n            col1, col2 = st.columns(2)\n            \n            with col1:\n                status_counts = {}\n                for instance in instances:\n                    status = instance['Status'].split()[1] if len(instance['Status'].split()) > 1 else instance['Status']\n                    status_counts[status] = status_counts.get(status, 0) + 1\n                \n                fig_pie = px.pie(\n                    values=list(status_counts.values()),\n                    names=list(status_counts.keys()),\n                    title=\"Instance Status Distribution\"\n                )\n                st.plotly_chart(fig_pie, use_container_width=True)\n            \n            with col2:\n                # Port usage chart\n                ports = [instance['Port'] for instance in instances]\n                statuses = [instance['Status'] for instance in instances]\n                \n                colors = ['green' if 'Running' in status else 'red' for status in statuses]\n                \n                fig_bar = go.Figure(data=[\n                    go.Bar(x=ports, y=[1]*len(ports), marker_color=colors)\n                ])\n                fig_bar.update_layout(\n                    title=\"Port Usage Status\",\n                    xaxis_title=\"Port Number\",\n                    yaxis_title=\"Status\",\n                    showlegend=False\n                )\n                st.plotly_chart(fig_bar, use_container_width=True)\n            \n            # Instance details table\n            st.subheader(\"📋 Detailed Instance Information\")\n            df = pd.DataFrame(instances)\n            st.dataframe(df, use_container_width=True)\n        \n        else:\n            st.info(\"No instances to monitor. Create some instances first!\")\n    \n    with tab4:\n        st.header(\"⚙️ System Settings\")\n        \n        col1, col2 = st.columns(2)\n        \n        with col1:\n            st.subheader(\"🔧 Default Settings\")\n            \n            current_auto_nginx = manager.config[\"settings\"].get(\"auto_nginx\", True)\n            current_auto_ssl = manager.config[\"settings\"].get(\"auto_ssl\", True)\n            \n            new_auto_nginx = st.checkbox(\"Auto-configure Nginx by default\", value=current_auto_nginx)\n            new_auto_ssl = st.checkbox(\"Auto-setup SSL by default\", value=current_auto_ssl)\n            \n            if st.button(\"💾 Save Settings\"):\n                manager.config[\"settings\"][\"auto_nginx\"] = new_auto_nginx\n                manager.config[\"settings\"][\"auto_ssl\"] = new_auto_ssl\n                manager.save_config()\n                st.success(\"Settings saved!\")\n        \n        with col2:\n            st.subheader(\"🧹 Maintenance\")\n            \n            if st.button(\"🔄 Reload Nginx\"):\n                try:\n                    import subprocess\n                    subprocess.run([\"sudo\", \"systemctl\", \"reload\", \"nginx\"], check=True)\n                    st.success(\"Nginx reloaded successfully!\")\n                except:\n                    st.error(\"Failed to reload Nginx\")\n            \n            if st.button(\"📋 Show SSL Certificates\"):\n                try:\n                    import subprocess\n                    result = subprocess.run([\"sudo\", \"certbot\", \"certificates\"], \n                                          capture_output=True, text=True)\n                    st.code(result.stdout)\n                except:\n                    st.error(\"Failed to get SSL certificate information\")\n            \n            if st.button(\"🧹 Clean Up Unused Configs\"):\n                st.info(\"This feature will be implemented in the next update\")\n\nelse:\n    st.error(\"Authentication required. Please log in to access the dashboard.\")\n"}