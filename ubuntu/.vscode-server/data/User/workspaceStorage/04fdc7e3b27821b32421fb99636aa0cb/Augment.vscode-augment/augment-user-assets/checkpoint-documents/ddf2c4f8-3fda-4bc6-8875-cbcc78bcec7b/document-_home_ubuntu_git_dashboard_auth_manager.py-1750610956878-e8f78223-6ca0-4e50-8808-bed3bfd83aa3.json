{"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/auth_manager.py"}, "originalCode": "#!/usr/bin/env python3\n\"\"\"\nAlgoFactory Dashboard Authentication Manager\nSecure authentication system with session management for Streamlit.\n\"\"\"\n\nimport streamlit as st\nimport hashlib\nimport hmac\nimport json\nimport os\nimport time\nfrom pathlib import Path\nfrom datetime import datetime, timedelta\nimport secrets\nimport bcrypt\n\nclass AuthManager:\n    def __init__(self, config_file=\"auth_config.json\"):\n        self.config_file = Path(config_file)\n        self.session_timeout = 86400  # 24 hours in seconds (longer session)\n        self.max_login_attempts = 5\n        self.lockout_duration = 900  # 15 minutes in seconds\n        self.load_config()\n    \n    def load_config(self):\n        \"\"\"Load authentication configuration.\"\"\"\n        try:\n            if self.config_file.exists():\n                with open(self.config_file, 'r') as f:\n                    self.config = json.load(f)\n            else:\n                # Create default config\n                self.config = {\n                    \"users\": {},\n                    \"failed_attempts\": {},\n                    \"settings\": {\n                        \"session_timeout\": self.session_timeout,\n                        \"max_login_attempts\": self.max_login_attempts,\n                        \"lockout_duration\": self.lockout_duration,\n                        \"require_strong_passwords\": True,\n                        \"enable_2fa\": False\n                    }\n                }\n                self.save_config()\n        except Exception as e:\n            st.error(f\"Error loading auth config: {e}\")\n            self.config = {\"users\": {}, \"failed_attempts\": {}, \"settings\": {}}\n    \n    def save_config(self):\n        \"\"\"Save authentication configuration.\"\"\"\n        try:\n            with open(self.config_file, 'w') as f:\n                json.dump(self.config, f, indent=2)\n        except Exception as e:\n            st.error(f\"Error saving auth config: {e}\")\n    \n    def hash_password(self, password):\n        \"\"\"Hash password using bcrypt.\"\"\"\n        salt = bcrypt.gensalt()\n        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')\n    \n    def verify_password(self, password, hashed):\n        \"\"\"Verify password against hash.\"\"\"\n        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))\n    \n    def is_strong_password(self, password):\n        \"\"\"Check if password meets strength requirements.\"\"\"\n        if len(password) < 8:\n            return False, \"Password must be at least 8 characters long\"\n        \n        has_upper = any(c.isupper() for c in password)\n        has_lower = any(c.islower() for c in password)\n        has_digit = any(c.isdigit() for c in password)\n        has_special = any(c in \"!@#$%^&*()_+-=[]{}|;:,.<>?\" for c in password)\n        \n        if not (has_upper and has_lower and has_digit and has_special):\n            return False, \"Password must contain uppercase, lowercase, digit, and special character\"\n        \n        return True, \"Password is strong\"\n    \n    def add_user(self, username, password, role=\"user\", email=\"\"):\n        \"\"\"Add a new user.\"\"\"\n        if username in self.config[\"users\"]:\n            return False, \"User already exists\"\n        \n        if self.config[\"settings\"].get(\"require_strong_passwords\", True):\n            is_strong, message = self.is_strong_password(password)\n            if not is_strong:\n                return False, message\n        \n        hashed_password = self.hash_password(password)\n        \n        self.config[\"users\"][username] = {\n            \"password_hash\": hashed_password,\n            \"role\": role,\n            \"email\": email,\n            \"created_at\": datetime.now().isoformat(),\n            \"last_login\": None,\n            \"active\": True\n        }\n        \n        self.save_config()\n        return True, \"User created successfully\"\n    \n    def authenticate_user(self, username, password):\n        \"\"\"Authenticate user credentials.\"\"\"\n        # Check if user exists\n        if username not in self.config[\"users\"]:\n            return False, \"Invalid username or password\"\n        \n        user = self.config[\"users\"][username]\n        \n        # Check if user is active\n        if not user.get(\"active\", True):\n            return False, \"Account is disabled\"\n        \n        # Check for account lockout\n        if self.is_account_locked(username):\n            lockout_time = self.config[\"failed_attempts\"][username][\"lockout_until\"]\n            return False, f\"Account locked until {lockout_time}\"\n        \n        # Verify password\n        if self.verify_password(password, user[\"password_hash\"]):\n            # Reset failed attempts on successful login\n            if username in self.config[\"failed_attempts\"]:\n                del self.config[\"failed_attempts\"][username]\n            \n            # Update last login\n            self.config[\"users\"][username][\"last_login\"] = datetime.now().isoformat()\n            self.save_config()\n            \n            return True, \"Authentication successful\"\n        else:\n            # Record failed attempt\n            self.record_failed_attempt(username)\n            return False, \"Invalid username or password\"\n    \n    def record_failed_attempt(self, username):\n        \"\"\"Record a failed login attempt.\"\"\"\n        now = datetime.now()\n        \n        if username not in self.config[\"failed_attempts\"]:\n            self.config[\"failed_attempts\"][username] = {\n                \"count\": 0,\n                \"last_attempt\": None,\n                \"lockout_until\": None\n            }\n        \n        self.config[\"failed_attempts\"][username][\"count\"] += 1\n        self.config[\"failed_attempts\"][username][\"last_attempt\"] = now.isoformat()\n        \n        # Lock account if max attempts reached\n        if self.config[\"failed_attempts\"][username][\"count\"] >= self.max_login_attempts:\n            lockout_until = now + timedelta(seconds=self.lockout_duration)\n            self.config[\"failed_attempts\"][username][\"lockout_until\"] = lockout_until.isoformat()\n        \n        self.save_config()\n    \n    def is_account_locked(self, username):\n        \"\"\"Check if account is currently locked.\"\"\"\n        if username not in self.config[\"failed_attempts\"]:\n            return False\n        \n        lockout_until = self.config[\"failed_attempts\"][username].get(\"lockout_until\")\n        if not lockout_until:\n            return False\n        \n        lockout_time = datetime.fromisoformat(lockout_until)\n        return datetime.now() < lockout_time\n    \n    def create_session(self, username):\n        \"\"\"Create a new session for authenticated user.\"\"\"\n        session_id = secrets.token_urlsafe(32)\n        session_data = {\n            \"username\": username,\n            \"role\": self.config[\"users\"][username][\"role\"],\n            \"created_at\": time.time(),\n            \"last_activity\": time.time()\n        }\n        \n        # Store in Streamlit session state\n        st.session_state[\"auth_session_id\"] = session_id\n        st.session_state[\"auth_session_data\"] = session_data\n        st.session_state[\"authenticated\"] = True\n        st.session_state[\"username\"] = username\n        st.session_state[\"user_role\"] = session_data[\"role\"]\n        \n        return session_id\n    \n    def validate_session(self):\n        \"\"\"Validate current session.\"\"\"\n        if not hasattr(st.session_state, \"auth_session_data\"):\n            return False\n        \n        session_data = st.session_state.auth_session_data\n        current_time = time.time()\n        \n        # Check session timeout\n        if current_time - session_data[\"last_activity\"] > self.session_timeout:\n            self.logout()\n            return False\n        \n        # Update last activity\n        session_data[\"last_activity\"] = current_time\n        st.session_state.auth_session_data = session_data\n        \n        return True\n    \n    def logout(self):\n        \"\"\"Logout current user.\"\"\"\n        # Clear session state\n        for key in [\"auth_session_id\", \"auth_session_data\", \"authenticated\", \"username\", \"user_role\"]:\n            if key in st.session_state:\n                del st.session_state[key]\n    \n    def is_authenticated(self):\n        \"\"\"Check if user is currently authenticated.\"\"\"\n        return st.session_state.get(\"authenticated\", False) and self.validate_session()\n    \n    def get_current_user(self):\n        \"\"\"Get current authenticated user info.\"\"\"\n        if self.is_authenticated():\n            username = st.session_state.get(\"username\")\n            if username and username in self.config[\"users\"]:\n                user_data = self.config[\"users\"][username].copy()\n                user_data[\"username\"] = username\n                # Don't return password hash\n                user_data.pop(\"password_hash\", None)\n                return user_data\n        return None\n    \n    def require_auth(self, allowed_roles=None):\n        \"\"\"Decorator/function to require authentication.\"\"\"\n        if not self.is_authenticated():\n            return False\n        \n        if allowed_roles:\n            user_role = st.session_state.get(\"user_role\", \"user\")\n            if user_role not in allowed_roles:\n                st.error(\"Insufficient permissions\")\n                return False\n        \n        return True\n    \n    def show_login_form(self):\n        \"\"\"Display login form.\"\"\"\n        st.title(\"🔐 AlgoFactory Dashboard Login\")\n        \n        with st.form(\"login_form\"):\n            st.markdown(\"### Please enter your credentials\")\n            username = st.text_input(\"Username\", placeholder=\"Enter your username\")\n            password = st.text_input(\"Password\", type=\"password\", placeholder=\"Enter your password\")\n            \n            col1, col2 = st.columns([1, 1])\n            with col1:\n                login_button = st.form_submit_button(\"🔑 Login\", use_container_width=True)\n            \n            if login_button:\n                if username and password:\n                    success, message = self.authenticate_user(username, password)\n                    if success:\n                        self.create_session(username)\n                        st.success(\"Login successful! Redirecting...\")\n                        st.rerun()\n                    else:\n                        st.error(f\"Login failed: {message}\")\n                else:\n                    st.error(\"Please enter both username and password\")\n        \n        # Show additional info\n        st.markdown(\"---\")\n        st.info(\"🛡️ This dashboard is protected. Please contact your administrator for access.\")\n        \n        # Show failed attempts warning if applicable\n        if username and username in self.config.get(\"failed_attempts\", {}):\n            attempts = self.config[\"failed_attempts\"][username][\"count\"]\n            remaining = self.max_login_attempts - attempts\n            if remaining > 0:\n                st.warning(f\"⚠️ {attempts} failed login attempts. {remaining} attempts remaining before lockout.\")\n", "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nAlgoFactory Dashboard Authentication Manager\nSecure authentication system with session management for Streamlit.\n\"\"\"\n\nimport streamlit as st\nimport hashlib\nimport hmac\nimport json\nimport os\nimport time\nfrom pathlib import Path\nfrom datetime import datetime, timedelta\nimport secrets\nimport bcrypt\n\nclass AuthManager:\n    def __init__(self, config_file=\"auth_config.json\"):\n        self.config_file = Path(config_file)\n        self.session_timeout = 86400  # 24 hours in seconds (longer session)\n        self.max_login_attempts = 5\n        self.lockout_duration = 900  # 15 minutes in seconds\n        self.load_config()\n    \n    def load_config(self):\n        \"\"\"Load authentication configuration.\"\"\"\n        try:\n            if self.config_file.exists():\n                with open(self.config_file, 'r') as f:\n                    self.config = json.load(f)\n            else:\n                # Create default config\n                self.config = {\n                    \"users\": {},\n                    \"failed_attempts\": {},\n                    \"settings\": {\n                        \"session_timeout\": self.session_timeout,\n                        \"max_login_attempts\": self.max_login_attempts,\n                        \"lockout_duration\": self.lockout_duration,\n                        \"require_strong_passwords\": True,\n                        \"enable_2fa\": False\n                    }\n                }\n                self.save_config()\n        except Exception as e:\n            st.error(f\"Error loading auth config: {e}\")\n            self.config = {\"users\": {}, \"failed_attempts\": {}, \"settings\": {}}\n    \n    def save_config(self):\n        \"\"\"Save authentication configuration.\"\"\"\n        try:\n            with open(self.config_file, 'w') as f:\n                json.dump(self.config, f, indent=2)\n        except Exception as e:\n            st.error(f\"Error saving auth config: {e}\")\n    \n    def hash_password(self, password):\n        \"\"\"Hash password using bcrypt.\"\"\"\n        salt = bcrypt.gensalt()\n        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')\n    \n    def verify_password(self, password, hashed):\n        \"\"\"Verify password against hash.\"\"\"\n        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))\n    \n    def is_strong_password(self, password):\n        \"\"\"Check if password meets strength requirements.\"\"\"\n        if len(password) < 8:\n            return False, \"Password must be at least 8 characters long\"\n        \n        has_upper = any(c.isupper() for c in password)\n        has_lower = any(c.islower() for c in password)\n        has_digit = any(c.isdigit() for c in password)\n        has_special = any(c in \"!@#$%^&*()_+-=[]{}|;:,.<>?\" for c in password)\n        \n        if not (has_upper and has_lower and has_digit and has_special):\n            return False, \"Password must contain uppercase, lowercase, digit, and special character\"\n        \n        return True, \"Password is strong\"\n    \n    def add_user(self, username, password, role=\"user\", email=\"\"):\n        \"\"\"Add a new user.\"\"\"\n        if username in self.config[\"users\"]:\n            return False, \"User already exists\"\n        \n        if self.config[\"settings\"].get(\"require_strong_passwords\", True):\n            is_strong, message = self.is_strong_password(password)\n            if not is_strong:\n                return False, message\n        \n        hashed_password = self.hash_password(password)\n        \n        self.config[\"users\"][username] = {\n            \"password_hash\": hashed_password,\n            \"role\": role,\n            \"email\": email,\n            \"created_at\": datetime.now().isoformat(),\n            \"last_login\": None,\n            \"active\": True\n        }\n        \n        self.save_config()\n        return True, \"User created successfully\"\n    \n    def authenticate_user(self, username, password):\n        \"\"\"Authenticate user credentials.\"\"\"\n        # Check if user exists\n        if username not in self.config[\"users\"]:\n            return False, \"Invalid username or password\"\n        \n        user = self.config[\"users\"][username]\n        \n        # Check if user is active\n        if not user.get(\"active\", True):\n            return False, \"Account is disabled\"\n        \n        # Check for account lockout\n        if self.is_account_locked(username):\n            lockout_time = self.config[\"failed_attempts\"][username][\"lockout_until\"]\n            return False, f\"Account locked until {lockout_time}\"\n        \n        # Verify password\n        if self.verify_password(password, user[\"password_hash\"]):\n            # Reset failed attempts on successful login\n            if username in self.config[\"failed_attempts\"]:\n                del self.config[\"failed_attempts\"][username]\n            \n            # Update last login\n            self.config[\"users\"][username][\"last_login\"] = datetime.now().isoformat()\n            self.save_config()\n            \n            return True, \"Authentication successful\"\n        else:\n            # Record failed attempt\n            self.record_failed_attempt(username)\n            return False, \"Invalid username or password\"\n    \n    def record_failed_attempt(self, username):\n        \"\"\"Record a failed login attempt.\"\"\"\n        now = datetime.now()\n        \n        if username not in self.config[\"failed_attempts\"]:\n            self.config[\"failed_attempts\"][username] = {\n                \"count\": 0,\n                \"last_attempt\": None,\n                \"lockout_until\": None\n            }\n        \n        self.config[\"failed_attempts\"][username][\"count\"] += 1\n        self.config[\"failed_attempts\"][username][\"last_attempt\"] = now.isoformat()\n        \n        # Lock account if max attempts reached\n        if self.config[\"failed_attempts\"][username][\"count\"] >= self.max_login_attempts:\n            lockout_until = now + timedelta(seconds=self.lockout_duration)\n            self.config[\"failed_attempts\"][username][\"lockout_until\"] = lockout_until.isoformat()\n        \n        self.save_config()\n    \n    def is_account_locked(self, username):\n        \"\"\"Check if account is currently locked.\"\"\"\n        if username not in self.config[\"failed_attempts\"]:\n            return False\n        \n        lockout_until = self.config[\"failed_attempts\"][username].get(\"lockout_until\")\n        if not lockout_until:\n            return False\n        \n        lockout_time = datetime.fromisoformat(lockout_until)\n        return datetime.now() < lockout_time\n    \n    def create_session(self, username):\n        \"\"\"Create a new session for authenticated user.\"\"\"\n        session_id = secrets.token_urlsafe(32)\n        session_data = {\n            \"username\": username,\n            \"role\": self.config[\"users\"][username][\"role\"],\n            \"created_at\": time.time(),\n            \"last_activity\": time.time()\n        }\n        \n        # Store in Streamlit session state\n        st.session_state[\"auth_session_id\"] = session_id\n        st.session_state[\"auth_session_data\"] = session_data\n        st.session_state[\"authenticated\"] = True\n        st.session_state[\"username\"] = username\n        st.session_state[\"user_role\"] = session_data[\"role\"]\n        \n        return session_id\n    \n    def validate_session(self):\n        \"\"\"Validate current session.\"\"\"\n        if not hasattr(st.session_state, \"auth_session_data\"):\n            return False\n        \n        session_data = st.session_state.auth_session_data\n        current_time = time.time()\n        \n        # Check session timeout\n        if current_time - session_data[\"last_activity\"] > self.session_timeout:\n            self.logout()\n            return False\n        \n        # Update last activity\n        session_data[\"last_activity\"] = current_time\n        st.session_state.auth_session_data = session_data\n        \n        return True\n    \n    def logout(self):\n        \"\"\"Logout current user.\"\"\"\n        # Clear session state\n        for key in [\"auth_session_id\", \"auth_session_data\", \"authenticated\", \"username\", \"user_role\"]:\n            if key in st.session_state:\n                del st.session_state[key]\n    \n    def is_authenticated(self):\n        \"\"\"Check if user is currently authenticated.\"\"\"\n        return st.session_state.get(\"authenticated\", False) and self.validate_session()\n    \n    def get_current_user(self):\n        \"\"\"Get current authenticated user info.\"\"\"\n        if self.is_authenticated():\n            username = st.session_state.get(\"username\")\n            if username and username in self.config[\"users\"]:\n                user_data = self.config[\"users\"][username].copy()\n                user_data[\"username\"] = username\n                # Don't return password hash\n                user_data.pop(\"password_hash\", None)\n                return user_data\n        return None\n    \n    def require_auth(self, allowed_roles=None):\n        \"\"\"Decorator/function to require authentication.\"\"\"\n        if not self.is_authenticated():\n            return False\n        \n        if allowed_roles:\n            user_role = st.session_state.get(\"user_role\", \"user\")\n            if user_role not in allowed_roles:\n                st.error(\"Insufficient permissions\")\n                return False\n        \n        return True\n    \n    def show_login_form(self):\n        \"\"\"Display login form.\"\"\"\n        st.title(\"🔐 AlgoFactory Dashboard Login\")\n        \n        with st.form(\"login_form\"):\n            st.markdown(\"### Please enter your credentials\")\n            username = st.text_input(\"Username\", placeholder=\"Enter your username\")\n            password = st.text_input(\"Password\", type=\"password\", placeholder=\"Enter your password\")\n            \n            col1, col2 = st.columns([1, 1])\n            with col1:\n                login_button = st.form_submit_button(\"🔑 Login\", use_container_width=True)\n            \n            if login_button:\n                if username and password:\n                    success, message = self.authenticate_user(username, password)\n                    if success:\n                        self.create_session(username)\n                        st.success(\"Login successful! Redirecting...\")\n                        st.rerun()\n                    else:\n                        st.error(f\"Login failed: {message}\")\n                else:\n                    st.error(\"Please enter both username and password\")\n        \n        # Show additional info\n        st.markdown(\"---\")\n        st.info(\"🛡️ This dashboard is protected. Please contact your administrator for access.\")\n        \n        # Show failed attempts warning if applicable\n        if username and username in self.config.get(\"failed_attempts\", {}):\n            attempts = self.config[\"failed_attempts\"][username][\"count\"]\n            remaining = self.max_login_attempts - attempts\n            if remaining > 0:\n                st.warning(f\"⚠️ {attempts} failed login attempts. {remaining} attempts remaining before lockout.\")\n"}