{"path": {"rootPath": "/home", "relPath": "ubuntu/git/dashboard/automated_instance_manager.py"}, "originalCode": "#!/usr/bin/env python3\n\"\"\"\nAutomated AlgoFactory Instance Manager\nComplete automation for instance creation, nginx, SSL, and management.\n\"\"\"\n\nimport os\nimport sys\nimport json\nimport time\nimport psutil\nimport subprocess\nimport requests\nimport shutil\nfrom pathlib import Path\nfrom datetime import datetime\n\nclass AutomatedInstanceManager:\n    def __init__(self):\n        self.base_dir = Path(__file__).parent.parent\n        self.config_file = Path(__file__).parent / \"automated_instances.json\"\n        self.nginx_config_dir = self.base_dir / \"nginx-config\"\n        self.algofactory_template = self.base_dir / \"algofactory\"\n        self.shared_venv = Path(\"/home/<USER>/shared-venv\")\n        self.load_config()\n    \n    def load_config(self):\n        \"\"\"Load instances configuration.\"\"\"\n        if self.config_file.exists():\n            with open(self.config_file, 'r') as f:\n                self.config = json.load(f)\n        else:\n            self.config = {\"instances\": {}, \"settings\": {\"auto_ssl\": True, \"auto_nginx\": True}}\n    \n    def save_config(self):\n        \"\"\"Save instances configuration.\"\"\"\n        with open(self.config_file, 'w') as f:\n            json.dump(self.config, f, indent=2)\n    \n    def create_instance(self, port, name=None, auto_start=True):\n        \"\"\"Create a complete AlgoFactory instance with nginx and SSL.\"\"\"\n        if name is None:\n            name = f\"algofactory-{port}\"\n        \n        print(f\"🚀 Creating automated instance: {name} on port {port}\")\n        \n        try:\n            # Step 1: Create instance directory\n            instance_dir = self.base_dir / name\n            if instance_dir.exists():\n                print(f\"⚠️  Instance directory already exists: {instance_dir}\")\n                return False\n            \n            print(f\"📁 Creating instance directory: {instance_dir}\")\n            shutil.copytree(self.algofactory_template, instance_dir)\n            \n            # Step 2: Configure .env file\n            self._configure_env_file(instance_dir, port, name)\n            \n            # Step 3: Create nginx configuration\n            if self.config[\"settings\"][\"auto_nginx\"]:\n                self._create_nginx_config(port, name)\n            \n            # Step 4: Get SSL certificate\n            if self.config[\"settings\"][\"auto_ssl\"]:\n                self._setup_ssl_certificate(port)\n            \n            # Step 5: Create systemd service\n            self._create_systemd_service(port, name, instance_dir)\n            \n            # Step 6: Update configuration\n            self.config[\"instances\"][str(port)] = {\n                \"name\": name,\n                \"port\": port,\n                \"created\": datetime.now().isoformat(),\n                \"auto_start\": auto_start,\n                \"nginx_enabled\": self.config[\"settings\"][\"auto_nginx\"],\n                \"ssl_enabled\": self.config[\"settings\"][\"auto_ssl\"],\n                \"service_name\": f\"algofactory-{port}\"\n            }\n            self.save_config()\n            \n            # Step 7: Start instance if requested\n            if auto_start:\n                self.start_instance(port)\n            \n            print(f\"✅ Successfully created automated instance {name} on port {port}\")\n            return True\n            \n        except Exception as e:\n            print(f\"❌ Error creating instance {name}: {e}\")\n            return False\n    \n    def _configure_env_file(self, instance_dir, port, name):\n        \"\"\"Configure .env file for the instance.\"\"\"\n        env_file = instance_dir / \".env\"\n\n        # Read template .env\n        with open(env_file, 'r') as f:\n            content = f.read()\n\n        # Calculate port numbers based on main port\n        # For port 1015: WebSocket=12015, ZMQ=15015\n        # For port 5000: WebSocket=12000, ZMQ=15000\n        port_offset = port % 100  # Get last two digits (15 for 1015, 00 for 5000)\n        websocket_port = 12000 + port_offset  # 12000 + 15 = 12015\n        zmq_port = 15000 + port_offset        # 15000 + 15 = 15015\n\n        # Update port and domain configurations\n        domain = f\"{port}.algofactory.in\"\n\n        replacements = {\n            \"FLASK_PORT='5000'\": f\"FLASK_PORT='{port}'\",\n            \"HOST_SERVER = 'https://5000.algofactory.in'\": f\"HOST_SERVER = 'https://{domain}'\",\n            \"REDIRECT_URL = 'https://5000.algofactory.in/angel/callback'\": f\"REDIRECT_URL = 'https://{domain}/angel/callback'\",\n            \"CORS_ALLOWED_ORIGINS = 'https://5000.algofactory.in'\": f\"CORS_ALLOWED_ORIGINS = 'https://{domain}'\",\n            \"WEBSOCKET_HOST='localhost'\": \"WEBSOCKET_HOST='127.0.0.1'\",  # Secure localhost binding\n            \"ZMQ_HOST='localhost'\": \"ZMQ_HOST='127.0.0.1'\",  # Secure localhost binding\n            \"WEBSOCKET_PORT='8765'\": f\"WEBSOCKET_PORT='{websocket_port}'\",  # Dynamic WebSocket port\n            \"ZMQ_PORT='5555'\": f\"ZMQ_PORT='{zmq_port}'\"  # Dynamic ZMQ port\n        }\n\n        for old, new in replacements.items():\n            content = content.replace(old, new)\n\n        # Write updated .env\n        with open(env_file, 'w') as f:\n            f.write(content)\n\n        print(f\"📝 Configured .env file for port {port}\")\n        print(f\"   - Flask Port: {port}\")\n        print(f\"   - WebSocket Port: {websocket_port}\")\n        print(f\"   - ZMQ Port: {zmq_port}\")\n    \n    def _create_nginx_config(self, port, name):\n        \"\"\"Create nginx configuration for the instance.\"\"\"\n        domain = f\"{port}.algofactory.in\"\n        config_content = f\"\"\"# Nginx configuration for {name}\n# Domain: {domain}\n# Backend: Flask on port {port}\n\n# Rate limiting zones\nlimit_req_zone $binary_remote_addr zone=algofactory_{port}_login:10m rate=10r/m;\nlimit_req_zone $binary_remote_addr zone=algofactory_{port}_general:10m rate=100r/m;\n\n# HTTP to HTTPS redirect\nserver {{\n    listen 80;\n    listen [::]:80;\n    server_name {domain};\n    \n    # Redirect all HTTP traffic to HTTPS\n    return 301 https://$server_name$request_uri;\n}}\n\n# HTTPS server block\nserver {{\n    listen 443 ssl http2;\n    listen [::]:443 ssl http2;\n    server_name {domain};\n\n    # SSL Configuration\n    ssl_certificate /etc/letsencrypt/live/{domain}/fullchain.pem;\n    ssl_certificate_key /etc/letsencrypt/live/{domain}/privkey.pem;\n    \n    # SSL Security Settings\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;\n    ssl_prefer_server_ciphers off;\n    ssl_session_cache shared:SSL:10m;\n    ssl_session_timeout 10m;\n    \n    # Security Headers\n    add_header X-Frame-Options \"SAMEORIGIN\" always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    add_header X-Content-Type-Options \"nosniff\" always;\n    add_header Referrer-Policy \"strict-origin-when-cross-origin\" always;\n    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;\n    \n    # Hide nginx version\n    server_tokens off;\n    \n    # Gzip compression\n    gzip on;\n    gzip_vary on;\n    gzip_min_length 1024;\n    gzip_proxied expired no-cache no-store private auth;\n    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;\n\n    # Logging\n    access_log /var/log/nginx/{domain}.access.log;\n    error_log /var/log/nginx/{domain}.error.log;\n\n    # Rate limiting for login attempts\n    location ~* /(login|auth|api/auth) {{\n        limit_req zone=algofactory_{port}_login burst=5 nodelay;\n        \n        proxy_pass http://127.0.0.1:{port};\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $host;\n        proxy_set_header X-Forwarded-Port $server_port;\n        \n        proxy_buffering off;\n        proxy_cache off;\n        proxy_set_header Cache-Control no-cache;\n        \n        proxy_connect_timeout 60s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n    }}\n\n    # WebSocket connections for real-time updates\n    location /socket.io/ {{\n        proxy_pass http://127.0.0.1:{port};\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # WebSocket specific settings\n        proxy_buffering off;\n        proxy_cache off;\n        proxy_read_timeout 86400;\n        proxy_send_timeout 86400;\n    }}\n\n    # API endpoints with higher rate limits\n    location /api/ {{\n        limit_req zone=algofactory_{port}_general burst=20 nodelay;\n        \n        proxy_pass http://127.0.0.1:{port};\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $host;\n        proxy_set_header X-Forwarded-Port $server_port;\n        \n        proxy_connect_timeout 30s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }}\n\n    # Main application with general rate limiting\n    location / {{\n        # Apply general rate limiting\n        limit_req zone=algofactory_{port}_general burst=20 nodelay;\n        \n        proxy_pass http://127.0.0.1:{port};\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $host;\n        proxy_set_header X-Forwarded-Port $server_port;\n        \n        # Flask specific headers\n        proxy_buffering off;\n        proxy_cache off;\n        \n        # Timeout settings\n        proxy_connect_timeout 60s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n    }}\n    \n    # Static files optimization\n    location ~* \\\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {{\n        proxy_pass http://127.0.0.1:{port};\n        proxy_set_header Host $host;\n        expires 1y;\n        add_header Cache-Control \"public, immutable\";\n    }}\n    \n    # Block access to sensitive files\n    location ~* \\\\.(env|config|json|log|bak|backup|old|db)$ {{\n        deny all;\n        return 404;\n    }}\n    \n    # Block access to hidden files\n    location ~ /\\\\. {{\n        deny all;\n        return 404;\n    }}\n    \n    # Security: Block common attack patterns\n    location ~* (eval\\\\(|base64_decode|gzinflate|shell_exec|passthru|system\\\\() {{\n        deny all;\n        return 403;\n    }}\n}}\"\"\"\n        \n        # Save nginx config\n        config_file = self.nginx_config_dir / f\"{domain}.conf\"\n        with open(config_file, 'w') as f:\n            f.write(config_content)\n        \n        # Deploy to nginx\n        subprocess.run([\n            \"sudo\", \"cp\", str(config_file), \"/etc/nginx/sites-available/\"\n        ], check=True)\n        \n        subprocess.run([\n            \"sudo\", \"ln\", \"-sf\", f\"/etc/nginx/sites-available/{domain}.conf\", \n            f\"/etc/nginx/sites-enabled/{domain}.conf\"\n        ], check=True)\n        \n        print(f\"🌐 Created nginx configuration for {domain}\")\n    \n    def _setup_ssl_certificate(self, port):\n        \"\"\"Setup SSL certificate for the instance.\"\"\"\n        domain = f\"{port}.algofactory.in\"\n        \n        try:\n            # Stop nginx temporarily\n            subprocess.run([\"sudo\", \"systemctl\", \"stop\", \"nginx\"], check=True)\n            \n            # Get SSL certificate\n            subprocess.run([\n                \"sudo\", \"certbot\", \"certonly\", \"--standalone\",\n                \"--email\", \"<EMAIL>\",\n                \"--agree-tos\", \"--no-eff-email\",\n                \"-d\", domain\n            ], check=True, input=\"2\\n\", text=True)  # Auto-select renew option\n            \n            # Start nginx\n            subprocess.run([\"sudo\", \"systemctl\", \"start\", \"nginx\"], check=True)\n            \n            print(f\"🔒 SSL certificate obtained for {domain}\")\n            \n        except subprocess.CalledProcessError as e:\n            print(f\"⚠️  SSL setup failed for {domain}: {e}\")\n            # Start nginx anyway\n            subprocess.run([\"sudo\", \"systemctl\", \"start\", \"nginx\"])\n    \n    def _create_systemd_service(self, port, name, instance_dir):\n        \"\"\"Create systemd service for auto-start.\"\"\"\n        service_name = f\"algofactory-{port}\"\n        service_content = f\"\"\"[Unit]\nDescription=AlgoFactory Instance {name} - Port {port}\nAfter=network.target\n\n[Service]\nType=simple\nUser=ubuntu\nGroup=ubuntu\nWorkingDirectory={instance_dir}\nEnvironment=PATH={self.shared_venv}/bin:/usr/local/bin:/usr/bin:/bin\nExecStart={self.shared_venv}/bin/python3 app.py\nRestart=always\nRestartSec=10\nStandardOutput=journal\nStandardError=journal\nMemoryMax=300M\n\n[Install]\nWantedBy=multi-user.target\"\"\"\n        \n        # Write service file\n        service_file = f\"/tmp/{service_name}.service\"\n        with open(service_file, 'w') as f:\n            f.write(service_content)\n        \n        # Install service\n        subprocess.run([\n            \"sudo\", \"cp\", service_file, f\"/etc/systemd/system/{service_name}.service\"\n        ], check=True)\n        \n        subprocess.run([\"sudo\", \"systemctl\", \"daemon-reload\"], check=True)\n        subprocess.run([\"sudo\", \"systemctl\", \"enable\", f\"{service_name}.service\"], check=True)\n        \n        print(f\"🔧 Created systemd service: {service_name}\")\n        \n        # Clean up temp file\n        os.remove(service_file)\n\n    def delete_instance(self, port):\n        \"\"\"Completely delete an instance and all its configurations.\"\"\"\n        port_str = str(port)\n        if port_str not in self.config[\"instances\"]:\n            print(f\"❌ Instance on port {port} not found\")\n            return False\n\n        instance_info = self.config[\"instances\"][port_str]\n        name = instance_info[\"name\"]\n        domain = f\"{port}.algofactory.in\"\n\n        print(f\"🗑️  Deleting instance {name} on port {port}...\")\n\n        try:\n            # Step 1: Stop and disable service\n            service_name = f\"algofactory-{port}\"\n            subprocess.run([\"sudo\", \"systemctl\", \"stop\", f\"{service_name}.service\"],\n                         capture_output=True)\n            subprocess.run([\"sudo\", \"systemctl\", \"disable\", f\"{service_name}.service\"],\n                         capture_output=True)\n            subprocess.run([\"sudo\", \"rm\", \"-f\", f\"/etc/systemd/system/{service_name}.service\"],\n                         capture_output=True)\n            subprocess.run([\"sudo\", \"systemctl\", \"daemon-reload\"], capture_output=True)\n\n            # Step 2: Remove nginx configuration\n            subprocess.run([\"sudo\", \"rm\", \"-f\", f\"/etc/nginx/sites-enabled/{domain}.conf\"],\n                         capture_output=True)\n            subprocess.run([\"sudo\", \"rm\", \"-f\", f\"/etc/nginx/sites-available/{domain}.conf\"],\n                         capture_output=True)\n\n            # Step 3: Remove local nginx config\n            local_config = self.nginx_config_dir / f\"{domain}.conf\"\n            if local_config.exists():\n                local_config.unlink()\n\n            # Step 4: Reload nginx\n            subprocess.run([\"sudo\", \"nginx\", \"-t\"], capture_output=True)\n            subprocess.run([\"sudo\", \"systemctl\", \"reload\", \"nginx\"], capture_output=True)\n\n            # Step 5: Remove SSL certificate (optional - keep for reuse)\n            # subprocess.run([\"sudo\", \"certbot\", \"delete\", \"--cert-name\", domain],\n            #              capture_output=True)\n\n            # Step 6: Remove instance directory\n            instance_dir = self.base_dir / name\n            if instance_dir.exists():\n                shutil.rmtree(instance_dir)\n\n            # Step 7: Update configuration\n            del self.config[\"instances\"][port_str]\n            self.save_config()\n\n            print(f\"✅ Successfully deleted instance {name}\")\n            return True\n\n        except Exception as e:\n            print(f\"❌ Error deleting instance {name}: {e}\")\n            return False\n\n    def start_instance(self, port):\n        \"\"\"Start an instance using systemd service.\"\"\"\n        service_name = f\"algofactory-{port}\"\n        try:\n            subprocess.run([\"sudo\", \"systemctl\", \"start\", f\"{service_name}.service\"], check=True)\n            time.sleep(3)  # Wait for startup\n\n            status = self.check_instance_status(port)\n            if \"Running\" in status:\n                print(f\"✅ Started instance on port {port}\")\n                return True\n            else:\n                print(f\"❌ Failed to start instance on port {port}\")\n                return False\n        except subprocess.CalledProcessError as e:\n            print(f\"❌ Error starting instance on port {port}: {e}\")\n            return False\n\n    def stop_instance(self, port):\n        \"\"\"Stop an instance using systemd service.\"\"\"\n        service_name = f\"algofactory-{port}\"\n        try:\n            subprocess.run([\"sudo\", \"systemctl\", \"stop\", f\"{service_name}.service\"], check=True)\n            time.sleep(2)  # Wait for shutdown\n\n            status = self.check_instance_status(port)\n            if \"Stopped\" in status:\n                print(f\"✅ Stopped instance on port {port}\")\n                return True\n            else:\n                print(f\"⚠️  Instance on port {port} may still be running\")\n                return False\n        except subprocess.CalledProcessError as e:\n            print(f\"❌ Error stopping instance on port {port}: {e}\")\n            return False\n\n    def restart_instance(self, port):\n        \"\"\"Restart an instance using systemd service.\"\"\"\n        service_name = f\"algofactory-{port}\"\n        try:\n            subprocess.run([\"sudo\", \"systemctl\", \"restart\", f\"{service_name}.service\"], check=True)\n            time.sleep(3)  # Wait for restart\n\n            status = self.check_instance_status(port)\n            if \"Running\" in status:\n                print(f\"✅ Restarted instance on port {port}\")\n                return True\n            else:\n                print(f\"❌ Failed to restart instance on port {port}\")\n                return False\n        except subprocess.CalledProcessError as e:\n            print(f\"❌ Error restarting instance on port {port}: {e}\")\n            return False\n\n    def check_instance_status(self, port):\n        \"\"\"Check if instance is running on given port.\"\"\"\n        try:\n            # Check if port is in use\n            for conn in psutil.net_connections():\n                if conn.laddr.port == port and conn.status == 'LISTEN':\n                    # Try to access the web interface\n                    try:\n                        response = requests.get(f\"http://127.0.0.1:{port}\", timeout=2)\n                        if response.status_code == 200:\n                            return \"🟢 Running\"\n                        else:\n                            return \"🟡 Port Used\"\n                    except:\n                        return \"🟡 Port Used\"\n            return \"🔴 Stopped\"\n        except:\n            return \"❓ Unknown\"\n\n    def get_instances(self):\n        \"\"\"Get list of all instances with enhanced information.\"\"\"\n        instances = []\n        for port_str, info in self.config.get(\"instances\", {}).items():\n            port = int(port_str)\n            domain = f\"{port}.algofactory.in\"\n            instances.append({\n                \"Port\": port,\n                \"Name\": info.get(\"name\", f\"algofactory-{port}\"),\n                \"Status\": self.check_instance_status(port),\n                \"Domain\": domain,\n                \"URL\": f\"https://{domain}\",\n                \"Created\": info.get(\"created\", \"Unknown\"),\n                \"Auto Start\": info.get(\"auto_start\", False),\n                \"SSL\": info.get(\"ssl_enabled\", False),\n                \"Nginx\": info.get(\"nginx_enabled\", False)\n            })\n        return sorted(instances, key=lambda x: x[\"Port\"])\n\n    def get_available_ports(self, start_port=1010, end_port=1020):\n        \"\"\"Get list of available ports in range.\"\"\"\n        used_ports = set(int(p) for p in self.config.get(\"instances\", {}).keys())\n        available = []\n\n        for port in range(start_port, end_port + 1):\n            if port not in used_ports:\n                # Check if port is actually free\n                try:\n                    for conn in psutil.net_connections():\n                        if conn.laddr.port == port:\n                            break\n                    else:\n                        available.append(port)\n                except:\n                    available.append(port)  # Assume available if can't check\n\n        return available\n\n    def bulk_create_instances(self, ports, auto_start=False):\n        \"\"\"Create multiple instances at once.\"\"\"\n        results = []\n        for port in ports:\n            success = self.create_instance(port, auto_start=auto_start)\n            results.append({\"port\": port, \"success\": success})\n        return results\n\n    def get_system_status(self):\n        \"\"\"Get overall system status.\"\"\"\n        instances = self.get_instances()\n        running_count = len([i for i in instances if \"Running\" in i[\"Status\"]])\n\n        return {\n            \"total_instances\": len(instances),\n            \"running_instances\": running_count,\n            \"stopped_instances\": len(instances) - running_count,\n            \"nginx_status\": self._check_nginx_status(),\n            \"ssl_certificates\": self._count_ssl_certificates(),\n            \"available_ports\": len(self.get_available_ports())\n        }\n\n    def _check_nginx_status(self):\n        \"\"\"Check nginx service status.\"\"\"\n        try:\n            result = subprocess.run([\"sudo\", \"systemctl\", \"is-active\", \"nginx\"],\n                                  capture_output=True, text=True)\n            return \"🟢 Active\" if result.returncode == 0 else \"🔴 Inactive\"\n        except:\n            return \"❓ Unknown\"\n\n    def _count_ssl_certificates(self):\n        \"\"\"Count SSL certificates.\"\"\"\n        try:\n            result = subprocess.run([\"sudo\", \"certbot\", \"certificates\"],\n                                  capture_output=True, text=True)\n            # Count certificate entries\n            count = result.stdout.count(\"Certificate Name:\")\n            return count\n        except:\n            return 0\n", "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nAutomated AlgoFactory Instance Manager\nComplete automation for instance creation, nginx, SSL, and management.\n\"\"\"\n\nimport os\nimport sys\nimport json\nimport time\nimport psutil\nimport subprocess\nimport requests\nimport shutil\nfrom pathlib import Path\nfrom datetime import datetime\n\nclass AutomatedInstanceManager:\n    def __init__(self):\n        self.base_dir = Path(__file__).parent.parent\n        self.config_file = Path(__file__).parent / \"automated_instances.json\"\n        self.nginx_config_dir = self.base_dir / \"nginx-config\"\n        self.algofactory_template = self.base_dir / \"algofactory\"\n        self.shared_venv = Path(\"/home/<USER>/shared-venv\")\n        self.load_config()\n    \n    def load_config(self):\n        \"\"\"Load instances configuration.\"\"\"\n        if self.config_file.exists():\n            with open(self.config_file, 'r') as f:\n                self.config = json.load(f)\n        else:\n            self.config = {\"instances\": {}, \"settings\": {\"auto_ssl\": True, \"auto_nginx\": True}}\n    \n    def save_config(self):\n        \"\"\"Save instances configuration.\"\"\"\n        with open(self.config_file, 'w') as f:\n            json.dump(self.config, f, indent=2)\n    \n    def create_instance(self, port, name=None, auto_start=True):\n        \"\"\"Create a complete AlgoFactory instance with nginx and SSL.\"\"\"\n        if name is None:\n            name = f\"algofactory-{port}\"\n        \n        print(f\"🚀 Creating automated instance: {name} on port {port}\")\n        \n        try:\n            # Step 1: Create instance directory\n            instance_dir = self.base_dir / name\n            if instance_dir.exists():\n                print(f\"⚠️  Instance directory already exists: {instance_dir}\")\n                return False\n            \n            print(f\"📁 Creating instance directory: {instance_dir}\")\n            shutil.copytree(self.algofactory_template, instance_dir)\n            \n            # Step 2: Configure .env file\n            self._configure_env_file(instance_dir, port, name)\n            \n            # Step 3: Create nginx configuration\n            if self.config[\"settings\"][\"auto_nginx\"]:\n                self._create_nginx_config(port, name)\n            \n            # Step 4: Get SSL certificate\n            if self.config[\"settings\"][\"auto_ssl\"]:\n                self._setup_ssl_certificate(port)\n            \n            # Step 5: Create systemd service\n            self._create_systemd_service(port, name, instance_dir)\n            \n            # Step 6: Update configuration\n            self.config[\"instances\"][str(port)] = {\n                \"name\": name,\n                \"port\": port,\n                \"created\": datetime.now().isoformat(),\n                \"auto_start\": auto_start,\n                \"nginx_enabled\": self.config[\"settings\"][\"auto_nginx\"],\n                \"ssl_enabled\": self.config[\"settings\"][\"auto_ssl\"],\n                \"service_name\": f\"algofactory-{port}\"\n            }\n            self.save_config()\n            \n            # Step 7: Start instance if requested\n            if auto_start:\n                self.start_instance(port)\n            \n            print(f\"✅ Successfully created automated instance {name} on port {port}\")\n            return True\n            \n        except Exception as e:\n            print(f\"❌ Error creating instance {name}: {e}\")\n            return False\n    \n    def _configure_env_file(self, instance_dir, port, name):\n        \"\"\"Configure .env file for the instance.\"\"\"\n        env_file = instance_dir / \".env\"\n\n        # Read template .env\n        with open(env_file, 'r') as f:\n            content = f.read()\n\n        # Calculate port numbers based on main port\n        # For port 1015: WebSocket=12015, ZMQ=15015\n        # For port 5000: WebSocket=12000, ZMQ=15000\n        port_offset = port % 100  # Get last two digits (15 for 1015, 00 for 5000)\n        websocket_port = 12000 + port_offset  # 12000 + 15 = 12015\n        zmq_port = 15000 + port_offset        # 15000 + 15 = 15015\n\n        # Update port and domain configurations\n        domain = f\"{port}.algofactory.in\"\n\n        replacements = {\n            \"FLASK_PORT='5000'\": f\"FLASK_PORT='{port}'\",\n            \"HOST_SERVER = 'https://5000.algofactory.in'\": f\"HOST_SERVER = 'https://{domain}'\",\n            \"REDIRECT_URL = 'https://5000.algofactory.in/angel/callback'\": f\"REDIRECT_URL = 'https://{domain}/angel/callback'\",\n            \"CORS_ALLOWED_ORIGINS = 'https://5000.algofactory.in'\": f\"CORS_ALLOWED_ORIGINS = 'https://{domain}'\",\n            \"WEBSOCKET_HOST='localhost'\": \"WEBSOCKET_HOST='127.0.0.1'\",  # Secure localhost binding\n            \"ZMQ_HOST='localhost'\": \"ZMQ_HOST='127.0.0.1'\",  # Secure localhost binding\n            \"WEBSOCKET_PORT='8765'\": f\"WEBSOCKET_PORT='{websocket_port}'\",  # Dynamic WebSocket port\n            \"ZMQ_PORT='5555'\": f\"ZMQ_PORT='{zmq_port}'\"  # Dynamic ZMQ port\n        }\n\n        for old, new in replacements.items():\n            content = content.replace(old, new)\n\n        # Write updated .env\n        with open(env_file, 'w') as f:\n            f.write(content)\n\n        print(f\"📝 Configured .env file for port {port}\")\n        print(f\"   - Flask Port: {port}\")\n        print(f\"   - WebSocket Port: {websocket_port}\")\n        print(f\"   - ZMQ Port: {zmq_port}\")\n    \n    def _create_nginx_config(self, port, name):\n        \"\"\"Create nginx configuration for the instance.\"\"\"\n        domain = f\"{port}.algofactory.in\"\n        config_content = f\"\"\"# Nginx configuration for {name}\n# Domain: {domain}\n# Backend: Flask on port {port}\n\n# Rate limiting zones\nlimit_req_zone $binary_remote_addr zone=algofactory_{port}_login:10m rate=10r/m;\nlimit_req_zone $binary_remote_addr zone=algofactory_{port}_general:10m rate=100r/m;\n\n# HTTP to HTTPS redirect\nserver {{\n    listen 80;\n    listen [::]:80;\n    server_name {domain};\n    \n    # Redirect all HTTP traffic to HTTPS\n    return 301 https://$server_name$request_uri;\n}}\n\n# HTTPS server block\nserver {{\n    listen 443 ssl http2;\n    listen [::]:443 ssl http2;\n    server_name {domain};\n\n    # SSL Configuration\n    ssl_certificate /etc/letsencrypt/live/{domain}/fullchain.pem;\n    ssl_certificate_key /etc/letsencrypt/live/{domain}/privkey.pem;\n    \n    # SSL Security Settings\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;\n    ssl_prefer_server_ciphers off;\n    ssl_session_cache shared:SSL:10m;\n    ssl_session_timeout 10m;\n    \n    # Security Headers\n    add_header X-Frame-Options \"SAMEORIGIN\" always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    add_header X-Content-Type-Options \"nosniff\" always;\n    add_header Referrer-Policy \"strict-origin-when-cross-origin\" always;\n    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;\n    \n    # Hide nginx version\n    server_tokens off;\n    \n    # Gzip compression\n    gzip on;\n    gzip_vary on;\n    gzip_min_length 1024;\n    gzip_proxied expired no-cache no-store private auth;\n    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;\n\n    # Logging\n    access_log /var/log/nginx/{domain}.access.log;\n    error_log /var/log/nginx/{domain}.error.log;\n\n    # Rate limiting for login attempts\n    location ~* /(login|auth|api/auth) {{\n        limit_req zone=algofactory_{port}_login burst=5 nodelay;\n        \n        proxy_pass http://127.0.0.1:{port};\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $host;\n        proxy_set_header X-Forwarded-Port $server_port;\n        \n        proxy_buffering off;\n        proxy_cache off;\n        proxy_set_header Cache-Control no-cache;\n        \n        proxy_connect_timeout 60s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n    }}\n\n    # WebSocket connections for real-time updates\n    location /socket.io/ {{\n        proxy_pass http://127.0.0.1:{port};\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # WebSocket specific settings\n        proxy_buffering off;\n        proxy_cache off;\n        proxy_read_timeout 86400;\n        proxy_send_timeout 86400;\n    }}\n\n    # API endpoints with higher rate limits\n    location /api/ {{\n        limit_req zone=algofactory_{port}_general burst=20 nodelay;\n        \n        proxy_pass http://127.0.0.1:{port};\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $host;\n        proxy_set_header X-Forwarded-Port $server_port;\n        \n        proxy_connect_timeout 30s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }}\n\n    # Main application with general rate limiting\n    location / {{\n        # Apply general rate limiting\n        limit_req zone=algofactory_{port}_general burst=20 nodelay;\n        \n        proxy_pass http://127.0.0.1:{port};\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $host;\n        proxy_set_header X-Forwarded-Port $server_port;\n        \n        # Flask specific headers\n        proxy_buffering off;\n        proxy_cache off;\n        \n        # Timeout settings\n        proxy_connect_timeout 60s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n    }}\n    \n    # Static files optimization\n    location ~* \\\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {{\n        proxy_pass http://127.0.0.1:{port};\n        proxy_set_header Host $host;\n        expires 1y;\n        add_header Cache-Control \"public, immutable\";\n    }}\n    \n    # Block access to sensitive files\n    location ~* \\\\.(env|config|json|log|bak|backup|old|db)$ {{\n        deny all;\n        return 404;\n    }}\n    \n    # Block access to hidden files\n    location ~ /\\\\. {{\n        deny all;\n        return 404;\n    }}\n    \n    # Security: Block common attack patterns\n    location ~* (eval\\\\(|base64_decode|gzinflate|shell_exec|passthru|system\\\\() {{\n        deny all;\n        return 403;\n    }}\n}}\"\"\"\n        \n        # Save nginx config\n        config_file = self.nginx_config_dir / f\"{domain}.conf\"\n        with open(config_file, 'w') as f:\n            f.write(config_content)\n        \n        # Deploy to nginx\n        subprocess.run([\n            \"sudo\", \"cp\", str(config_file), \"/etc/nginx/sites-available/\"\n        ], check=True)\n        \n        subprocess.run([\n            \"sudo\", \"ln\", \"-sf\", f\"/etc/nginx/sites-available/{domain}.conf\", \n            f\"/etc/nginx/sites-enabled/{domain}.conf\"\n        ], check=True)\n        \n        print(f\"🌐 Created nginx configuration for {domain}\")\n    \n    def _setup_ssl_certificate(self, port):\n        \"\"\"Setup SSL certificate for the instance.\"\"\"\n        domain = f\"{port}.algofactory.in\"\n        \n        try:\n            # Stop nginx temporarily\n            subprocess.run([\"sudo\", \"systemctl\", \"stop\", \"nginx\"], check=True)\n            \n            # Get SSL certificate\n            subprocess.run([\n                \"sudo\", \"certbot\", \"certonly\", \"--standalone\",\n                \"--email\", \"<EMAIL>\",\n                \"--agree-tos\", \"--no-eff-email\",\n                \"-d\", domain\n            ], check=True, input=\"2\\n\", text=True)  # Auto-select renew option\n            \n            # Start nginx\n            subprocess.run([\"sudo\", \"systemctl\", \"start\", \"nginx\"], check=True)\n            \n            print(f\"🔒 SSL certificate obtained for {domain}\")\n            \n        except subprocess.CalledProcessError as e:\n            print(f\"⚠️  SSL setup failed for {domain}: {e}\")\n            # Start nginx anyway\n            subprocess.run([\"sudo\", \"systemctl\", \"start\", \"nginx\"])\n    \n    def _create_systemd_service(self, port, name, instance_dir):\n        \"\"\"Create systemd service for auto-start.\"\"\"\n        service_name = f\"algofactory-{port}\"\n        service_content = f\"\"\"[Unit]\nDescription=AlgoFactory Instance {name} - Port {port}\nAfter=network.target\n\n[Service]\nType=simple\nUser=ubuntu\nGroup=ubuntu\nWorkingDirectory={instance_dir}\nEnvironment=PATH={self.shared_venv}/bin:/usr/local/bin:/usr/bin:/bin\nExecStart={self.shared_venv}/bin/python3 app.py\nRestart=always\nRestartSec=10\nStandardOutput=journal\nStandardError=journal\nMemoryMax=300M\n\n[Install]\nWantedBy=multi-user.target\"\"\"\n        \n        # Write service file\n        service_file = f\"/tmp/{service_name}.service\"\n        with open(service_file, 'w') as f:\n            f.write(service_content)\n        \n        # Install service\n        subprocess.run([\n            \"sudo\", \"cp\", service_file, f\"/etc/systemd/system/{service_name}.service\"\n        ], check=True)\n        \n        subprocess.run([\"sudo\", \"systemctl\", \"daemon-reload\"], check=True)\n        subprocess.run([\"sudo\", \"systemctl\", \"enable\", f\"{service_name}.service\"], check=True)\n        \n        print(f\"🔧 Created systemd service: {service_name}\")\n        \n        # Clean up temp file\n        os.remove(service_file)\n\n    def delete_instance(self, port):\n        \"\"\"Completely delete an instance and all its configurations.\"\"\"\n        port_str = str(port)\n        if port_str not in self.config[\"instances\"]:\n            print(f\"❌ Instance on port {port} not found\")\n            return False\n\n        instance_info = self.config[\"instances\"][port_str]\n        name = instance_info[\"name\"]\n        domain = f\"{port}.algofactory.in\"\n\n        print(f\"🗑️  Deleting instance {name} on port {port}...\")\n\n        try:\n            # Step 1: Stop and disable service\n            service_name = f\"algofactory-{port}\"\n            subprocess.run([\"sudo\", \"systemctl\", \"stop\", f\"{service_name}.service\"],\n                         capture_output=True)\n            subprocess.run([\"sudo\", \"systemctl\", \"disable\", f\"{service_name}.service\"],\n                         capture_output=True)\n            subprocess.run([\"sudo\", \"rm\", \"-f\", f\"/etc/systemd/system/{service_name}.service\"],\n                         capture_output=True)\n            subprocess.run([\"sudo\", \"systemctl\", \"daemon-reload\"], capture_output=True)\n\n            # Step 2: Remove nginx configuration\n            subprocess.run([\"sudo\", \"rm\", \"-f\", f\"/etc/nginx/sites-enabled/{domain}.conf\"],\n                         capture_output=True)\n            subprocess.run([\"sudo\", \"rm\", \"-f\", f\"/etc/nginx/sites-available/{domain}.conf\"],\n                         capture_output=True)\n\n            # Step 3: Remove local nginx config\n            local_config = self.nginx_config_dir / f\"{domain}.conf\"\n            if local_config.exists():\n                local_config.unlink()\n\n            # Step 4: Reload nginx\n            subprocess.run([\"sudo\", \"nginx\", \"-t\"], capture_output=True)\n            subprocess.run([\"sudo\", \"systemctl\", \"reload\", \"nginx\"], capture_output=True)\n\n            # Step 5: Remove SSL certificate (optional - keep for reuse)\n            # subprocess.run([\"sudo\", \"certbot\", \"delete\", \"--cert-name\", domain],\n            #              capture_output=True)\n\n            # Step 6: Remove instance directory\n            instance_dir = self.base_dir / name\n            if instance_dir.exists():\n                shutil.rmtree(instance_dir)\n\n            # Step 7: Update configuration\n            del self.config[\"instances\"][port_str]\n            self.save_config()\n\n            print(f\"✅ Successfully deleted instance {name}\")\n            return True\n\n        except Exception as e:\n            print(f\"❌ Error deleting instance {name}: {e}\")\n            return False\n\n    def start_instance(self, port):\n        \"\"\"Start an instance using systemd service.\"\"\"\n        service_name = f\"algofactory-{port}\"\n        try:\n            subprocess.run([\"sudo\", \"systemctl\", \"start\", f\"{service_name}.service\"], check=True)\n            time.sleep(3)  # Wait for startup\n\n            status = self.check_instance_status(port)\n            if \"Running\" in status:\n                print(f\"✅ Started instance on port {port}\")\n                return True\n            else:\n                print(f\"❌ Failed to start instance on port {port}\")\n                return False\n        except subprocess.CalledProcessError as e:\n            print(f\"❌ Error starting instance on port {port}: {e}\")\n            return False\n\n    def stop_instance(self, port):\n        \"\"\"Stop an instance using systemd service.\"\"\"\n        service_name = f\"algofactory-{port}\"\n        try:\n            subprocess.run([\"sudo\", \"systemctl\", \"stop\", f\"{service_name}.service\"], check=True)\n            time.sleep(2)  # Wait for shutdown\n\n            status = self.check_instance_status(port)\n            if \"Stopped\" in status:\n                print(f\"✅ Stopped instance on port {port}\")\n                return True\n            else:\n                print(f\"⚠️  Instance on port {port} may still be running\")\n                return False\n        except subprocess.CalledProcessError as e:\n            print(f\"❌ Error stopping instance on port {port}: {e}\")\n            return False\n\n    def restart_instance(self, port):\n        \"\"\"Restart an instance using systemd service.\"\"\"\n        service_name = f\"algofactory-{port}\"\n        try:\n            subprocess.run([\"sudo\", \"systemctl\", \"restart\", f\"{service_name}.service\"], check=True)\n            time.sleep(3)  # Wait for restart\n\n            status = self.check_instance_status(port)\n            if \"Running\" in status:\n                print(f\"✅ Restarted instance on port {port}\")\n                return True\n            else:\n                print(f\"❌ Failed to restart instance on port {port}\")\n                return False\n        except subprocess.CalledProcessError as e:\n            print(f\"❌ Error restarting instance on port {port}: {e}\")\n            return False\n\n    def check_instance_status(self, port):\n        \"\"\"Check if instance is running on given port.\"\"\"\n        try:\n            # Check if port is in use\n            for conn in psutil.net_connections():\n                if conn.laddr.port == port and conn.status == 'LISTEN':\n                    # Try to access the web interface\n                    try:\n                        response = requests.get(f\"http://127.0.0.1:{port}\", timeout=2)\n                        if response.status_code == 200:\n                            return \"🟢 Running\"\n                        else:\n                            return \"🟡 Port Used\"\n                    except:\n                        return \"🟡 Port Used\"\n            return \"🔴 Stopped\"\n        except:\n            return \"❓ Unknown\"\n\n    def get_instances(self):\n        \"\"\"Get list of all instances with enhanced information.\"\"\"\n        instances = []\n        for port_str, info in self.config.get(\"instances\", {}).items():\n            port = int(port_str)\n            domain = f\"{port}.algofactory.in\"\n            instances.append({\n                \"Port\": port,\n                \"Name\": info.get(\"name\", f\"algofactory-{port}\"),\n                \"Status\": self.check_instance_status(port),\n                \"Domain\": domain,\n                \"URL\": f\"https://{domain}\",\n                \"Created\": info.get(\"created\", \"Unknown\"),\n                \"Auto Start\": info.get(\"auto_start\", False),\n                \"SSL\": info.get(\"ssl_enabled\", False),\n                \"Nginx\": info.get(\"nginx_enabled\", False)\n            })\n        return sorted(instances, key=lambda x: x[\"Port\"])\n\n    def get_available_ports(self, start_port=1010, end_port=1020):\n        \"\"\"Get list of available ports in range.\"\"\"\n        used_ports = set(int(p) for p in self.config.get(\"instances\", {}).keys())\n        available = []\n\n        for port in range(start_port, end_port + 1):\n            if port not in used_ports:\n                # Check if port is actually free\n                try:\n                    for conn in psutil.net_connections():\n                        if conn.laddr.port == port:\n                            break\n                    else:\n                        available.append(port)\n                except:\n                    available.append(port)  # Assume available if can't check\n\n        return available\n\n    def bulk_create_instances(self, ports, auto_start=False):\n        \"\"\"Create multiple instances at once.\"\"\"\n        results = []\n        for port in ports:\n            success = self.create_instance(port, auto_start=auto_start)\n            results.append({\"port\": port, \"success\": success})\n        return results\n\n    def get_system_status(self):\n        \"\"\"Get overall system status.\"\"\"\n        instances = self.get_instances()\n        running_count = len([i for i in instances if \"Running\" in i[\"Status\"]])\n\n        return {\n            \"total_instances\": len(instances),\n            \"running_instances\": running_count,\n            \"stopped_instances\": len(instances) - running_count,\n            \"nginx_status\": self._check_nginx_status(),\n            \"ssl_certificates\": self._count_ssl_certificates(),\n            \"available_ports\": len(self.get_available_ports())\n        }\n\n    def _check_nginx_status(self):\n        \"\"\"Check nginx service status.\"\"\"\n        try:\n            result = subprocess.run([\"sudo\", \"systemctl\", \"is-active\", \"nginx\"],\n                                  capture_output=True, text=True)\n            return \"🟢 Active\" if result.returncode == 0 else \"🔴 Inactive\"\n        except:\n            return \"❓ Unknown\"\n\n    def _count_ssl_certificates(self):\n        \"\"\"Count SSL certificates.\"\"\"\n        try:\n            result = subprocess.run([\"sudo\", \"certbot\", \"certificates\"],\n                                  capture_output=True, text=True)\n            # Count certificate entries\n            count = result.stdout.count(\"Certificate Name:\")\n            return count\n        except:\n            return 0\n"}