{"path": {"rootPath": "/home", "relPath": "ubuntu/git/nginx-config/5000.algofactory.in-http.conf"}, "originalCode": "# Nginx configuration for AlgoFactory Main Application (HTTP Only - Testing)\n# Domain: 5000.algofactory.in\n# Backend: Flask on port 5000\n\n# Rate limiting zones\nlimit_req_zone $binary_remote_addr zone=algofactory_login:10m rate=10r/m;\nlimit_req_zone $binary_remote_addr zone=algofactory_general:10m rate=100r/m;\n\n# HTTP to HTTPS redirect\nserver {\n    listen 80;\n    listen [::]:80;\n    server_name 5000.algofactory.in **************;\n\n    # Redirect all HTTP traffic to HTTPS\n    return 301 https://$server_name$request_uri;\n}\n\n# HTTPS server block\nserver {\n    listen 443 ssl http2;\n    listen [::]:443 ssl http2;\n    server_name 5000.algofactory.in **************;\n\n    # SSL Configuration\n    ssl_certificate /etc/letsencrypt/live/5000.algofactory.in/fullchain.pem;\n    ssl_certificate_key /etc/letsencrypt/live/5000.algofactory.in/privkey.pem;\n\n    # SSL Security Settings\n    ssl_protocols TLSv1.2 TLSv1.3;\n    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;\n    ssl_prefer_server_ciphers off;\n    ssl_session_cache shared:SSL:10m;\n    ssl_session_timeout 10m;\n    \n    # Security Headers\n    add_header X-Frame-Options \"SAMEORIGIN\" always;\n    add_header X-XSS-Protection \"1; mode=block\" always;\n    add_header X-Content-Type-Options \"nosniff\" always;\n    add_header Referrer-Policy \"strict-origin-when-cross-origin\" always;\n    add_header Strict-Transport-Security \"max-age=31536000; includeSubDomains\" always;\n    \n    # Hide nginx version\n    server_tokens off;\n    \n    # Gzip compression\n    gzip on;\n    gzip_vary on;\n    gzip_min_length 1024;\n    gzip_proxied expired no-cache no-store private auth;\n    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;\n\n    # Logging\n    access_log /var/log/nginx/5000.algofactory.in.access.log;\n    error_log /var/log/nginx/5000.algofactory.in.error.log;\n\n    # Rate limiting for login attempts\n    location ~* /(login|auth|api/auth) {\n        limit_req zone=algofactory_login burst=5 nodelay;\n        \n        proxy_pass http://127.0.0.1:5000;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $host;\n        proxy_set_header X-Forwarded-Port $server_port;\n        \n        proxy_buffering off;\n        proxy_cache off;\n        proxy_set_header Cache-Control no-cache;\n        \n        proxy_connect_timeout 60s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n    }\n\n    # WebSocket connections for real-time updates\n    location /socket.io/ {\n        proxy_pass http://127.0.0.1:5000;\n        proxy_http_version 1.1;\n        proxy_set_header Upgrade $http_upgrade;\n        proxy_set_header Connection \"upgrade\";\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        \n        # WebSocket specific settings\n        proxy_buffering off;\n        proxy_cache off;\n        proxy_read_timeout 86400;\n        proxy_send_timeout 86400;\n    }\n\n    # API endpoints with higher rate limits\n    location /api/ {\n        limit_req zone=algofactory_general burst=20 nodelay;\n        \n        proxy_pass http://127.0.0.1:5000;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $host;\n        proxy_set_header X-Forwarded-Port $server_port;\n        \n        proxy_connect_timeout 30s;\n        proxy_send_timeout 30s;\n        proxy_read_timeout 30s;\n    }\n\n    # Main application with general rate limiting\n    location / {\n        # Apply general rate limiting\n        limit_req zone=algofactory_general burst=20 nodelay;\n        \n        proxy_pass http://127.0.0.1:5000;\n        proxy_http_version 1.1;\n        proxy_set_header Host $host;\n        proxy_set_header X-Real-IP $remote_addr;\n        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;\n        proxy_set_header X-Forwarded-Proto $scheme;\n        proxy_set_header X-Forwarded-Host $host;\n        proxy_set_header X-Forwarded-Port $server_port;\n        \n        # Flask specific headers\n        proxy_buffering off;\n        proxy_cache off;\n        \n        # Timeout settings\n        proxy_connect_timeout 60s;\n        proxy_send_timeout 60s;\n        proxy_read_timeout 60s;\n    }\n    \n    # Static files optimization\n    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {\n        proxy_pass http://127.0.0.1:5000;\n        proxy_set_header Host $host;\n        expires 1y;\n        add_header Cache-Control \"public, immutable\";\n    }\n    \n    # Block access to sensitive files\n    location ~* \\.(env|config|json|log|bak|backup|old|db)$ {\n        deny all;\n        return 404;\n    }\n    \n    # Block access to hidden files\n    location ~ /\\. {\n        deny all;\n        return 404;\n    }\n    \n    # Security: Block common attack patterns\n    location ~* (eval\\(|base64_decode|gzinflate|shell_exec|passthru|system\\() {\n        deny all;\n        return 403;\n    }\n}\n"}