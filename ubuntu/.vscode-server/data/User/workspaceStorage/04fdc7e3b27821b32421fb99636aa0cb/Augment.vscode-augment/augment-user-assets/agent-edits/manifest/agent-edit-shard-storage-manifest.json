{"version": 1, "lastUpdated": 1750605693919, "shards": {"shard-ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b": {"checkpointDocumentIds": ["ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/run_dashboard.py", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/8502.algofactory.in.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-nginx.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-ssl.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/algofactory-dashboard.service", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-service.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-firewall.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/DNS-SETUP-GUIDE.md", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-dashboard.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/README.md", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/auth_manager.py", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/user_manager.py", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/auth_config.py", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/8502.algofactory.in-secure.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-auth.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/dashboard/simple_dashboard.py", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-secure-dashboard.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/SECURITY-GUIDE.md", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/HOSTINGER-DNS-SETUP.md", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/dashboard.algofactory.in.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/client-template.algofactory.in.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-subdomains.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/setup-ssl-subdomains.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-new-client.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/manage-clients.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/deploy-complete-system.sh", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/COMPLETE-SETUP-GUIDE.md", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/algofactory/.env", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/5000.algofactory.in.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/backup.algofactory.in.conf", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/algofactory-dashboard.service", "ddf2c4f8-3fda-4bc6-8875-cbcc78bcec7b:/home/<USER>/git/nginx-config/5000.algofactory.in-http.conf"], "size": 2431125, "checkpointCount": 93, "lastModified": 1750605688760}}}