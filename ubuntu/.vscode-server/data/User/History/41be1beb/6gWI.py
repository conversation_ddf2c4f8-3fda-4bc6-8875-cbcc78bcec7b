#!/usr/bin/env python3
"""
AlgoFactory Dashboard Authentication Manager
Secure authentication system with session management for Streamlit.
"""

import streamlit as st
import hashlib
import hmac
import json
import os
import time
from pathlib import Path
from datetime import datetime, timedelta
import secrets
import bcrypt

class AuthManager:
    def __init__(self, config_file="auth_config.json"):
        self.config_file = Path(config_file)
        self.session_timeout = 86400  # 24 hours in seconds (longer session)
        self.max_login_attempts = 5
        self.lockout_duration = 900  # 15 minutes in seconds
        self.load_config()
    
    def load_config(self):
        """Load authentication configuration."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    self.config = json.load(f)
            else:
                # Create default config
                self.config = {
                    "users": {},
                    "failed_attempts": {},
                    "settings": {
                        "session_timeout": self.session_timeout,
                        "max_login_attempts": self.max_login_attempts,
                        "lockout_duration": self.lockout_duration,
                        "require_strong_passwords": True,
                        "enable_2fa": False
                    }
                }
                self.save_config()
        except Exception as e:
            st.error(f"Error loading auth config: {e}")
            self.config = {"users": {}, "failed_attempts": {}, "settings": {}}
    
    def save_config(self):
        """Save authentication configuration."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            st.error(f"Error saving auth config: {e}")
    
    def hash_password(self, password):
        """Hash password using bcrypt."""
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password, hashed):
        """Verify password against hash."""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def is_strong_password(self, password):
        """Check if password meets strength requirements."""
        if len(password) < 8:
            return False, "Password must be at least 8 characters long"
        
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        if not (has_upper and has_lower and has_digit and has_special):
            return False, "Password must contain uppercase, lowercase, digit, and special character"
        
        return True, "Password is strong"
    
    def add_user(self, username, password, role="user", email=""):
        """Add a new user."""
        if username in self.config["users"]:
            return False, "User already exists"
        
        if self.config["settings"].get("require_strong_passwords", True):
            is_strong, message = self.is_strong_password(password)
            if not is_strong:
                return False, message
        
        hashed_password = self.hash_password(password)
        
        self.config["users"][username] = {
            "password_hash": hashed_password,
            "role": role,
            "email": email,
            "created_at": datetime.now().isoformat(),
            "last_login": None,
            "active": True
        }
        
        self.save_config()
        return True, "User created successfully"
    
    def authenticate_user(self, username, password):
        """Authenticate user credentials."""
        # Check if user exists
        if username not in self.config["users"]:
            return False, "Invalid username or password"
        
        user = self.config["users"][username]
        
        # Check if user is active
        if not user.get("active", True):
            return False, "Account is disabled"
        
        # Check for account lockout
        if self.is_account_locked(username):
            lockout_time = self.config["failed_attempts"][username]["lockout_until"]
            return False, f"Account locked until {lockout_time}"
        
        # Verify password
        if self.verify_password(password, user["password_hash"]):
            # Reset failed attempts on successful login
            if username in self.config["failed_attempts"]:
                del self.config["failed_attempts"][username]
            
            # Update last login
            self.config["users"][username]["last_login"] = datetime.now().isoformat()
            self.save_config()
            
            return True, "Authentication successful"
        else:
            # Record failed attempt
            self.record_failed_attempt(username)
            return False, "Invalid username or password"
    
    def record_failed_attempt(self, username):
        """Record a failed login attempt."""
        now = datetime.now()
        
        if username not in self.config["failed_attempts"]:
            self.config["failed_attempts"][username] = {
                "count": 0,
                "last_attempt": None,
                "lockout_until": None
            }
        
        self.config["failed_attempts"][username]["count"] += 1
        self.config["failed_attempts"][username]["last_attempt"] = now.isoformat()
        
        # Lock account if max attempts reached
        if self.config["failed_attempts"][username]["count"] >= self.max_login_attempts:
            lockout_until = now + timedelta(seconds=self.lockout_duration)
            self.config["failed_attempts"][username]["lockout_until"] = lockout_until.isoformat()
        
        self.save_config()
    
    def is_account_locked(self, username):
        """Check if account is currently locked."""
        if username not in self.config["failed_attempts"]:
            return False
        
        lockout_until = self.config["failed_attempts"][username].get("lockout_until")
        if not lockout_until:
            return False
        
        lockout_time = datetime.fromisoformat(lockout_until)
        return datetime.now() < lockout_time
    
    def create_session(self, username, remember_me=True):
        """Create a new session for authenticated user."""
        session_id = secrets.token_urlsafe(32)
        # Use longer timeout if remember me is checked
        timeout = self.session_timeout if remember_me else 3600  # 24h vs 1h
        session_data = {
            "username": username,
            "role": self.config["users"][username]["role"],
            "created_at": time.time(),
            "last_activity": time.time(),
            "timeout": timeout,
            "remember_me": remember_me
        }
        
        # Store in Streamlit session state
        st.session_state["auth_session_id"] = session_id
        st.session_state["auth_session_data"] = session_data
        st.session_state["authenticated"] = True
        st.session_state["username"] = username
        st.session_state["user_role"] = session_data["role"]
        
        return session_id
    
    def validate_session(self):
        """Validate current session."""
        if not hasattr(st.session_state, "auth_session_data"):
            return False
        
        session_data = st.session_state.auth_session_data
        current_time = time.time()
        
        # Check session timeout
        if current_time - session_data["last_activity"] > self.session_timeout:
            self.logout()
            return False
        
        # Update last activity
        session_data["last_activity"] = current_time
        st.session_state.auth_session_data = session_data
        
        return True
    
    def logout(self):
        """Logout current user."""
        # Clear session state
        for key in ["auth_session_id", "auth_session_data", "authenticated", "username", "user_role"]:
            if key in st.session_state:
                del st.session_state[key]
    
    def is_authenticated(self):
        """Check if user is currently authenticated."""
        return st.session_state.get("authenticated", False) and self.validate_session()
    
    def get_current_user(self):
        """Get current authenticated user info."""
        if self.is_authenticated():
            username = st.session_state.get("username")
            if username and username in self.config["users"]:
                user_data = self.config["users"][username].copy()
                user_data["username"] = username
                # Don't return password hash
                user_data.pop("password_hash", None)
                return user_data
        return None
    
    def require_auth(self, allowed_roles=None):
        """Decorator/function to require authentication."""
        if not self.is_authenticated():
            return False
        
        if allowed_roles:
            user_role = st.session_state.get("user_role", "user")
            if user_role not in allowed_roles:
                st.error("Insufficient permissions")
                return False
        
        return True
    
    def show_login_form(self):
        """Display login form."""
        st.title("🔐 AlgoFactory Dashboard Login")
        
        with st.form("login_form"):
            st.markdown("### Please enter your credentials")
            username = st.text_input("Username", placeholder="Enter your username")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            remember_me = st.checkbox("Remember me for 24 hours", value=True)

            col1, col2 = st.columns([1, 1])
            with col1:
                login_button = st.form_submit_button("🔑 Login", use_container_width=True)
            
            if login_button:
                if username and password:
                    success, message = self.authenticate_user(username, password)
                    if success:
                        self.create_session(username)
                        st.success("Login successful! Redirecting...")
                        st.rerun()
                    else:
                        st.error(f"Login failed: {message}")
                else:
                    st.error("Please enter both username and password")
        
        # Show additional info
        st.markdown("---")
        st.info("🛡️ This dashboard is protected. Please contact your administrator for access.")
        
        # Show failed attempts warning if applicable
        if username and username in self.config.get("failed_attempts", {}):
            attempts = self.config["failed_attempts"][username]["count"]
            remaining = self.max_login_attempts - attempts
            if remaining > 0:
                st.warning(f"⚠️ {attempts} failed login attempts. {remaining} attempts remaining before lockout.")
