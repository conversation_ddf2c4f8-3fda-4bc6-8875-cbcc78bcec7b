#!/usr/bin/env python3
"""
Automated AlgoFactory Instance Manager
Complete automation for instance creation, nginx, SSL, and management.
"""

import os
import sys
import json
import time
import psutil
import subprocess
import requests
import shutil
from pathlib import Path
from datetime import datetime

class AutomatedInstanceManager:
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.config_file = Path(__file__).parent / "automated_instances.json"
        self.nginx_config_dir = self.base_dir / "nginx-config"
        self.algofactory_template = self.base_dir / "algofactory"
        self.shared_venv = Path("/home/<USER>/shared-venv")
        self.load_config()
    
    def load_config(self):
        """Load instances configuration."""
        if self.config_file.exists():
            with open(self.config_file, 'r') as f:
                self.config = json.load(f)
        else:
            self.config = {"instances": {}, "settings": {"auto_ssl": True, "auto_nginx": True}}
    
    def save_config(self):
        """Save instances configuration."""
        with open(self.config_file, 'w') as f:
            json.dump(self.config, f, indent=2)
    
    def create_instance(self, port, name=None, auto_start=True):
        """Create a complete AlgoFactory instance with nginx and SSL."""
        if name is None:
            name = f"algofactory-{port}"
        
        print(f"🚀 Creating automated instance: {name} on port {port}")
        
        try:
            # Step 1: Create instance directory
            instance_dir = self.base_dir / name
            if instance_dir.exists():
                print(f"⚠️  Instance directory already exists: {instance_dir}")
                return False

            print(f"📁 Creating instance directory: {instance_dir}")
            shutil.copytree(self.algofactory_template, instance_dir)

            # Remove the db folder to ensure fresh database
            db_dir = instance_dir / "db"
            if db_dir.exists():
                print(f"🗑️  Removing copied database folder: {db_dir}")
                shutil.rmtree(db_dir)
                print(f"📁 Creating empty db directory")
                db_dir.mkdir()
            
            # Step 2: Configure .env file
            self._configure_env_file(instance_dir, port, name)
            
            # Step 3: Create nginx configuration
            if self.config["settings"]["auto_nginx"]:
                self._create_nginx_config(port, name)
            
            # Step 4: Get SSL certificate
            if self.config["settings"]["auto_ssl"]:
                self._setup_ssl_certificate(port)
            
            # Step 5: Create systemd service
            self._create_systemd_service(port, name, instance_dir)
            
            # Step 6: Update configuration
            self.config["instances"][str(port)] = {
                "name": name,
                "port": port,
                "created": datetime.now().isoformat(),
                "auto_start": auto_start,
                "nginx_enabled": self.config["settings"]["auto_nginx"],
                "ssl_enabled": self.config["settings"]["auto_ssl"],
                "service_name": f"algofactory-{port}"
            }
            self.save_config()
            
            # Step 7: Start instance if requested
            if auto_start:
                self.start_instance(port)
            
            print(f"✅ Successfully created automated instance {name} on port {port}")
            return True
            
        except Exception as e:
            print(f"❌ Error creating instance {name}: {e}")
            return False
    
    def _configure_env_file(self, instance_dir, port, name):
        """Configure .env file for the instance."""
        env_file = instance_dir / ".env"

        # Read template .env
        with open(env_file, 'r') as f:
            content = f.read()

        # Calculate port numbers based on main port
        # For port 1015: WebSocket=12015, ZMQ=15015
        # For port 5000: WebSocket=12000, ZMQ=15000
        port_offset = port % 100  # Get last two digits (15 for 1015, 00 for 5000)
        websocket_port = 12000 + port_offset  # 12000 + 15 = 12015
        zmq_port = 15000 + port_offset        # 15000 + 15 = 15015

        # Update port and domain configurations
        domain = f"{port}.algofactory.in"

        replacements = {
            "FLASK_PORT='5000'": f"FLASK_PORT='{port}'",
            "HOST_SERVER = 'https://5000.algofactory.in'": f"HOST_SERVER = 'https://{domain}'",
            "REDIRECT_URL = 'https://5000.algofactory.in/angel/callback'": f"REDIRECT_URL = 'https://{domain}/angel/callback'",
            "CORS_ALLOWED_ORIGINS = 'https://5000.algofactory.in'": f"CORS_ALLOWED_ORIGINS = 'https://{domain}'",
            "WEBSOCKET_HOST='localhost'": "WEBSOCKET_HOST='127.0.0.1'",  # Secure localhost binding
            "ZMQ_HOST='localhost'": "ZMQ_HOST='127.0.0.1'",  # Secure localhost binding
            "WEBSOCKET_PORT='8765'": f"WEBSOCKET_PORT='{websocket_port}'",  # Dynamic WebSocket port
            "ZMQ_PORT='5555'": f"ZMQ_PORT='{zmq_port}'"  # Dynamic ZMQ port
        }

        for old, new in replacements.items():
            content = content.replace(old, new)

        # Write updated .env
        with open(env_file, 'w') as f:
            f.write(content)

        print(f"📝 Configured .env file for port {port}")
        print(f"   - Flask Port: {port}")
        print(f"   - WebSocket Port: {websocket_port}")
        print(f"   - ZMQ Port: {zmq_port}")
    
    def _create_nginx_config(self, port, name):
        """Create nginx configuration for the instance."""
        domain = f"{port}.algofactory.in"
        config_content = f"""# Nginx configuration for {name}
# Domain: {domain}
# Backend: Flask on port {port}

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=algofactory_{port}_login:10m rate=10r/m;
limit_req_zone $binary_remote_addr zone=algofactory_{port}_general:10m rate=100r/m;

# HTTP to HTTPS redirect
server {{
    listen 80;
    listen [::]:80;
    server_name {domain};
    
    # Redirect all HTTP traffic to HTTPS
    return 301 https://$server_name$request_uri;
}}

# HTTPS server block
server {{
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name {domain};

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/{domain}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/{domain}/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Hide nginx version
    server_tokens off;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;

    # Logging
    access_log /var/log/nginx/{domain}.access.log;
    error_log /var/log/nginx/{domain}.error.log;

    # Rate limiting for login attempts
    location ~* /(login|auth|api/auth) {{
        limit_req zone=algofactory_{port}_login burst=5 nodelay;
        
        proxy_pass http://127.0.0.1:{port};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Cache-Control no-cache;
        
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }}

    # WebSocket connections for real-time updates
    location /socket.io/ {{
        proxy_pass http://127.0.0.1:{port};
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket specific settings
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }}

    # API endpoints with higher rate limits
    location /api/ {{
        limit_req zone=algofactory_{port}_general burst=20 nodelay;
        
        proxy_pass http://127.0.0.1:{port};
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }}

    # Main application with general rate limiting
    location / {{
        # Apply general rate limiting
        limit_req zone=algofactory_{port}_general burst=20 nodelay;
        
        proxy_pass http://127.0.0.1:{port};
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # Flask specific headers
        proxy_buffering off;
        proxy_cache off;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }}
    
    # Static files optimization
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {{
        proxy_pass http://127.0.0.1:{port};
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }}
    
    # Block access to sensitive files
    location ~* \\.(env|config|json|log|bak|backup|old|db)$ {{
        deny all;
        return 404;
    }}
    
    # Block access to hidden files
    location ~ /\\. {{
        deny all;
        return 404;
    }}
    
    # Security: Block common attack patterns
    location ~* (eval\\(|base64_decode|gzinflate|shell_exec|passthru|system\\() {{
        deny all;
        return 403;
    }}
}}"""
        
        # Save nginx config
        config_file = self.nginx_config_dir / f"{domain}.conf"
        with open(config_file, 'w') as f:
            f.write(config_content)
        
        # Deploy to nginx
        subprocess.run([
            "sudo", "cp", str(config_file), "/etc/nginx/sites-available/"
        ], check=True)
        
        subprocess.run([
            "sudo", "ln", "-sf", f"/etc/nginx/sites-available/{domain}.conf", 
            f"/etc/nginx/sites-enabled/{domain}.conf"
        ], check=True)
        
        print(f"🌐 Created nginx configuration for {domain}")
    
    def _setup_ssl_certificate(self, port):
        """Setup SSL certificate for the instance."""
        domain = f"{port}.algofactory.in"
        
        try:
            # Stop nginx temporarily
            subprocess.run(["sudo", "systemctl", "stop", "nginx"], check=True)
            
            # Get SSL certificate
            subprocess.run([
                "sudo", "certbot", "certonly", "--standalone",
                "--email", "<EMAIL>",
                "--agree-tos", "--no-eff-email",
                "-d", domain
            ], check=True, input="2\n", text=True)  # Auto-select renew option
            
            # Start nginx
            subprocess.run(["sudo", "systemctl", "start", "nginx"], check=True)
            
            print(f"🔒 SSL certificate obtained for {domain}")
            
        except subprocess.CalledProcessError as e:
            print(f"⚠️  SSL setup failed for {domain}: {e}")
            # Start nginx anyway
            subprocess.run(["sudo", "systemctl", "start", "nginx"])
    
    def _create_systemd_service(self, port, name, instance_dir):
        """Create systemd service for auto-start."""
        service_name = f"algofactory-{port}"
        service_content = f"""[Unit]
Description=AlgoFactory Instance {name} - Port {port}
After=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory={instance_dir}
Environment=PATH={self.shared_venv}/bin:/usr/local/bin:/usr/bin:/bin
ExecStart={self.shared_venv}/bin/python3 app.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
MemoryMax=300M

[Install]
WantedBy=multi-user.target"""
        
        # Write service file
        service_file = f"/tmp/{service_name}.service"
        with open(service_file, 'w') as f:
            f.write(service_content)
        
        # Install service
        subprocess.run([
            "sudo", "cp", service_file, f"/etc/systemd/system/{service_name}.service"
        ], check=True)
        
        subprocess.run(["sudo", "systemctl", "daemon-reload"], check=True)
        subprocess.run(["sudo", "systemctl", "enable", f"{service_name}.service"], check=True)
        
        print(f"🔧 Created systemd service: {service_name}")
        
        # Clean up temp file
        os.remove(service_file)

    def delete_instance(self, port):
        """Completely delete an instance and all its configurations."""
        port_str = str(port)
        if port_str not in self.config["instances"]:
            print(f"❌ Instance on port {port} not found")
            return False

        instance_info = self.config["instances"][port_str]
        name = instance_info["name"]
        domain = f"{port}.algofactory.in"

        print(f"🗑️  Deleting instance {name} on port {port}...")

        try:
            # Step 1: Stop and disable service
            service_name = f"algofactory-{port}"
            subprocess.run(["sudo", "systemctl", "stop", f"{service_name}.service"],
                         capture_output=True)
            subprocess.run(["sudo", "systemctl", "disable", f"{service_name}.service"],
                         capture_output=True)
            subprocess.run(["sudo", "rm", "-f", f"/etc/systemd/system/{service_name}.service"],
                         capture_output=True)
            subprocess.run(["sudo", "systemctl", "daemon-reload"], capture_output=True)

            # Step 2: Remove nginx configuration
            subprocess.run(["sudo", "rm", "-f", f"/etc/nginx/sites-enabled/{domain}.conf"],
                         capture_output=True)
            subprocess.run(["sudo", "rm", "-f", f"/etc/nginx/sites-available/{domain}.conf"],
                         capture_output=True)

            # Step 3: Remove local nginx config
            local_config = self.nginx_config_dir / f"{domain}.conf"
            if local_config.exists():
                local_config.unlink()

            # Step 4: Reload nginx
            subprocess.run(["sudo", "nginx", "-t"], capture_output=True)
            subprocess.run(["sudo", "systemctl", "reload", "nginx"], capture_output=True)

            # Step 5: Remove SSL certificate (optional - keep for reuse)
            # subprocess.run(["sudo", "certbot", "delete", "--cert-name", domain],
            #              capture_output=True)

            # Step 6: Remove instance directory
            instance_dir = self.base_dir / name
            if instance_dir.exists():
                shutil.rmtree(instance_dir)

            # Step 7: Update configuration
            del self.config["instances"][port_str]
            self.save_config()

            print(f"✅ Successfully deleted instance {name}")
            return True

        except Exception as e:
            print(f"❌ Error deleting instance {name}: {e}")
            return False

    def start_instance(self, port):
        """Start an instance using systemd service."""
        service_name = f"algofactory-{port}"
        try:
            subprocess.run(["sudo", "systemctl", "start", f"{service_name}.service"], check=True)
            time.sleep(3)  # Wait for startup

            status = self.check_instance_status(port)
            if "Running" in status:
                print(f"✅ Started instance on port {port}")
                return True
            else:
                print(f"❌ Failed to start instance on port {port}")
                return False
        except subprocess.CalledProcessError as e:
            print(f"❌ Error starting instance on port {port}: {e}")
            return False

    def stop_instance(self, port):
        """Stop an instance using systemd service."""
        service_name = f"algofactory-{port}"
        try:
            subprocess.run(["sudo", "systemctl", "stop", f"{service_name}.service"], check=True)
            time.sleep(2)  # Wait for shutdown

            status = self.check_instance_status(port)
            if "Stopped" in status:
                print(f"✅ Stopped instance on port {port}")
                return True
            else:
                print(f"⚠️  Instance on port {port} may still be running")
                return False
        except subprocess.CalledProcessError as e:
            print(f"❌ Error stopping instance on port {port}: {e}")
            return False

    def restart_instance(self, port):
        """Restart an instance using systemd service."""
        service_name = f"algofactory-{port}"
        try:
            subprocess.run(["sudo", "systemctl", "restart", f"{service_name}.service"], check=True)
            time.sleep(3)  # Wait for restart

            status = self.check_instance_status(port)
            if "Running" in status:
                print(f"✅ Restarted instance on port {port}")
                return True
            else:
                print(f"❌ Failed to restart instance on port {port}")
                return False
        except subprocess.CalledProcessError as e:
            print(f"❌ Error restarting instance on port {port}: {e}")
            return False

    def check_instance_status(self, port):
        """Check if instance is running on given port."""
        try:
            # Check if port is in use
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == 'LISTEN':
                    # Try to access the web interface
                    try:
                        response = requests.get(f"http://127.0.0.1:{port}", timeout=2)
                        if response.status_code == 200:
                            return "🟢 Running"
                        else:
                            return "🟡 Port Used"
                    except:
                        return "🟡 Port Used"
            return "🔴 Stopped"
        except:
            return "❓ Unknown"

    def get_instances(self):
        """Get list of all instances with enhanced information."""
        instances = []
        for port_str, info in self.config.get("instances", {}).items():
            port = int(port_str)
            domain = f"{port}.algofactory.in"
            instances.append({
                "Port": port,
                "Name": info.get("name", f"algofactory-{port}"),
                "Status": self.check_instance_status(port),
                "Domain": domain,
                "URL": f"https://{domain}",
                "Created": info.get("created", "Unknown"),
                "Auto Start": info.get("auto_start", False),
                "SSL": info.get("ssl_enabled", False),
                "Nginx": info.get("nginx_enabled", False)
            })
        return sorted(instances, key=lambda x: x["Port"])

    def get_available_ports(self, start_port=1010, end_port=1020):
        """Get list of available ports in range."""
        used_ports = set(int(p) for p in self.config.get("instances", {}).keys())
        available = []

        for port in range(start_port, end_port + 1):
            if port not in used_ports:
                # Check if port is actually free
                try:
                    for conn in psutil.net_connections():
                        if conn.laddr.port == port:
                            break
                    else:
                        available.append(port)
                except:
                    available.append(port)  # Assume available if can't check

        return available

    def bulk_create_instances(self, ports, auto_start=False):
        """Create multiple instances at once."""
        results = []
        for port in ports:
            success = self.create_instance(port, auto_start=auto_start)
            results.append({"port": port, "success": success})
        return results

    def get_system_status(self):
        """Get overall system status."""
        instances = self.get_instances()
        running_count = len([i for i in instances if "Running" in i["Status"]])

        return {
            "total_instances": len(instances),
            "running_instances": running_count,
            "stopped_instances": len(instances) - running_count,
            "nginx_status": self._check_nginx_status(),
            "ssl_certificates": self._count_ssl_certificates(),
            "available_ports": len(self.get_available_ports())
        }

    def _check_nginx_status(self):
        """Check nginx service status."""
        try:
            result = subprocess.run(["sudo", "systemctl", "is-active", "nginx"],
                                  capture_output=True, text=True)
            return "🟢 Active" if result.returncode == 0 else "🔴 Inactive"
        except:
            return "❓ Unknown"

    def _count_ssl_certificates(self):
        """Count SSL certificates."""
        try:
            result = subprocess.run(["sudo", "certbot", "certificates"],
                                  capture_output=True, text=True)
            # Count certificate entries
            count = result.stdout.count("Certificate Name:")
            return count
        except:
            return 0
