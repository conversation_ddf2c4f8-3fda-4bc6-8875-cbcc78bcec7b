#!/usr/bin/env python3
"""
Enhanced AlgoFactory Dashboard with Automated Instance Management
Single-click instance creation, management, nginx, and SSL automation.
"""

import streamlit as st
import pandas as pd
import time
from automated_instance_manager import AutomatedInstanceManager
from auth_config import auth_config
import plotly.express as px
import plotly.graph_objects as go

# Page configuration
st.set_page_config(
    page_title="AlgoFactory Control Center",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Authentication check
if not auth_config.is_auth_enabled() or auth_config.check_authentication():
    
    # Initialize manager
    if 'manager' not in st.session_state:
        st.session_state.manager = AutomatedInstanceManager()
    
    manager = st.session_state.manager
    
    # Header
    st.title("🚀 AlgoFactory Control Center")
    st.markdown("**Automated Instance Management with Nginx & SSL**")
    
    # Sidebar
    with st.sidebar:
        st.header("🎛️ Control Panel")
        
        # System Status
        st.subheader("📊 System Status")
        status = manager.get_system_status()
        
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Total Instances", status["total_instances"])
            st.metric("SSL Certificates", status["ssl_certificates"])
        with col2:
            st.metric("Running", status["running_instances"])
            st.metric("Available Ports", status["available_ports"])
        
        st.markdown(f"**Nginx:** {status['nginx_status']}")
        
        # Quick Actions
        st.subheader("⚡ Quick Actions")

        if st.button("🔄 Refresh Data", use_container_width=True):
            st.rerun()

        if st.button("🚀 Start All Instances", use_container_width=True):
            with st.spinner("Starting all instances..."):
                instances = manager.get_instances()
                success_count = 0
                for instance in instances:
                    if "Stopped" in instance["Status"]:
                        if manager.start_instance(instance["Port"]):
                            success_count += 1
                st.success(f"Started {success_count} instances!")
            st.rerun()

        if st.button("🛑 Stop All Instances", use_container_width=True):
            with st.spinner("Stopping all instances..."):
                instances = manager.get_instances()
                success_count = 0
                for instance in instances:
                    if "Running" in instance["Status"]:
                        if manager.stop_instance(instance["Port"]):
                            success_count += 1
                st.success(f"Stopped {success_count} instances!")
            st.rerun()
    
    # Main content tabs
    tab1, tab2, tab3, tab4 = st.tabs(["📋 Instance Manager", "➕ Create Instance", "📊 Monitoring", "⚙️ Settings"])
    
    with tab1:
        st.header("📋 Instance Manager")
        
        # Get instances
        instances = manager.get_instances()
        
        if instances:
            # Create DataFrame
            df = pd.DataFrame(instances)
            
            # Display instances with action buttons
            for idx, instance in enumerate(instances):
                with st.container():
                    col1, col2, col3, col4, col5, col6, col7 = st.columns([1, 2, 1, 1, 1, 1, 1])
                    
                    with col1:
                        st.write(f"**{instance['Port']}**")
                    
                    with col2:
                        st.write(f"[{instance['Name']}]({instance['URL']})")
                        st.caption(f"{instance['Domain']}")
                    
                    with col3:
                        st.write(instance['Status'])
                    
                    with col4:
                        if "Running" in instance['Status']:
                            if st.button("🛑 Stop", key=f"stop_{instance['Port']}"):
                                with st.spinner(f"Stopping port {instance['Port']}..."):
                                    success = manager.stop_instance(instance['Port'])
                                if success:
                                    st.success(f"Stopped port {instance['Port']}")
                                else:
                                    st.error(f"Failed to stop port {instance['Port']}")
                                st.rerun()
                        else:
                            if st.button("▶️ Start", key=f"start_{instance['Port']}"):
                                with st.spinner(f"Starting port {instance['Port']}..."):
                                    success = manager.start_instance(instance['Port'])
                                if success:
                                    st.success(f"Started port {instance['Port']}")
                                else:
                                    st.error(f"Failed to start port {instance['Port']}")
                                st.rerun()
                    
                    with col5:
                        if st.button("🔄 Restart", key=f"restart_{instance['Port']}"):
                            with st.spinner(f"Restarting port {instance['Port']}..."):
                                success = manager.restart_instance(instance['Port'])
                            if success:
                                st.success(f"Restarted port {instance['Port']}")
                            else:
                                st.error(f"Failed to restart port {instance['Port']}")
                            st.rerun()
                    
                    with col6:
                        if st.button("🌐 Open", key=f"open_{instance['Port']}"):
                            st.markdown(f"[Open {instance['Domain']}]({instance['URL']})")
                    
                    with col7:
                        if st.button("🗑️ Delete", key=f"delete_{instance['Port']}"):
                            if st.session_state.get(f"confirm_delete_{instance['Port']}", False):
                                with st.spinner(f"Deleting instance {instance['Port']}..."):
                                    success = manager.delete_instance(instance['Port'])
                                if success:
                                    st.success(f"Deleted instance {instance['Port']}")
                                else:
                                    st.error(f"Failed to delete instance {instance['Port']}")
                                st.session_state[f"confirm_delete_{instance['Port']}"] = False
                                st.rerun()
                            else:
                                st.session_state[f"confirm_delete_{instance['Port']}"] = True
                                st.warning("Click again to confirm deletion")
                    
                    st.divider()
        else:
            st.info("No instances created yet. Use the 'Create Instance' tab to get started!")
    
    with tab2:
        st.header("➕ Create New Instance")
        
        col1, col2 = st.columns([2, 1])
        
        with col1:
            st.subheader("🔧 Instance Configuration")
            
            # Port selection
            available_ports = manager.get_available_ports()
            if available_ports:
                selected_port = st.selectbox(
                    "Select Port",
                    available_ports,
                    help="Choose from available ports (1010-1020)"
                )
            else:
                st.error("No available ports in range 1010-1020")
                selected_port = None
            
            # Instance name
            instance_name = st.text_input(
                "Instance Name",
                value=f"algofactory-{selected_port}" if selected_port else "",
                help="Custom name for this instance"
            )
            
            # Auto-start option
            auto_start = st.checkbox(
                "Auto-start after creation",
                value=True,
                help="Automatically start the instance after creation"
            )
            
            # Advanced options
            with st.expander("🔧 Advanced Options"):
                auto_nginx = st.checkbox("Auto-configure Nginx", value=True)
                auto_ssl = st.checkbox("Auto-setup SSL Certificate", value=True)
                
                if auto_ssl and not auto_nginx:
                    st.warning("SSL requires Nginx configuration")
        
        with col2:
            st.subheader("📋 Creation Summary")
            if selected_port:
                domain = f"{selected_port}.algofactory.in"
                st.write(f"**Port:** {selected_port}")
                st.write(f"**Domain:** {domain}")
                st.write(f"**URL:** https://{domain}")
                st.write(f"**Auto-start:** {'Yes' if auto_start else 'No'}")
                st.write(f"**Nginx:** {'Yes' if auto_nginx else 'No'}")
                st.write(f"**SSL:** {'Yes' if auto_ssl else 'No'}")
        
        # Create button
        if selected_port and instance_name:
            if st.button("🚀 Create Instance", type="primary", use_container_width=True):
                # Update manager settings
                manager.config["settings"]["auto_nginx"] = auto_nginx
                manager.config["settings"]["auto_ssl"] = auto_ssl
                manager.save_config()
                
                with st.spinner(f"Creating instance {instance_name} on port {selected_port}..."):
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    # Simulate progress updates
                    status_text.text("Creating instance directory...")
                    progress_bar.progress(20)
                    time.sleep(1)
                    
                    status_text.text("Configuring environment...")
                    progress_bar.progress(40)
                    
                    if auto_nginx:
                        status_text.text("Setting up Nginx configuration...")
                        progress_bar.progress(60)
                        time.sleep(1)
                    
                    if auto_ssl:
                        status_text.text("Obtaining SSL certificate...")
                        progress_bar.progress(80)
                        time.sleep(2)
                    
                    status_text.text("Finalizing setup...")
                    progress_bar.progress(90)
                    
                    # Actually create the instance
                    success = manager.create_instance(
                        selected_port, 
                        instance_name, 
                        auto_start=auto_start
                    )
                    
                    progress_bar.progress(100)
                    
                    if success:
                        st.success(f"✅ Successfully created instance {instance_name}!")
                        if auto_start:
                            st.info(f"🌐 Access your instance at: https://{domain}")
                        time.sleep(2)
                        st.rerun()
                    else:
                        st.error(f"❌ Failed to create instance {instance_name}")
        
        # Bulk creation
        st.subheader("📦 Bulk Creation")
        col1, col2 = st.columns(2)
        
        with col1:
            bulk_ports = st.multiselect(
                "Select Multiple Ports",
                available_ports,
                help="Create multiple instances at once"
            )
        
        with col2:
            bulk_auto_start = st.checkbox("Auto-start all", value=False)
        
        if bulk_ports:
            if st.button("🚀 Create All Selected", type="secondary"):
                with st.spinner(f"Creating {len(bulk_ports)} instances..."):
                    results = manager.bulk_create_instances(bulk_ports, auto_start=bulk_auto_start)
                    
                    success_count = sum(1 for r in results if r["success"])
                    st.success(f"Created {success_count}/{len(bulk_ports)} instances successfully!")
                    
                    for result in results:
                        if not result["success"]:
                            st.error(f"Failed to create instance on port {result['port']}")
                
                st.rerun()
    
    with tab3:
        st.header("📊 System Monitoring")
        
        # Get instances for monitoring
        instances = manager.get_instances()
        
        if instances:
            # Status distribution
            col1, col2 = st.columns(2)
            
            with col1:
                status_counts = {}
                for instance in instances:
                    status = instance['Status'].split()[1] if len(instance['Status'].split()) > 1 else instance['Status']
                    status_counts[status] = status_counts.get(status, 0) + 1
                
                fig_pie = px.pie(
                    values=list(status_counts.values()),
                    names=list(status_counts.keys()),
                    title="Instance Status Distribution"
                )
                st.plotly_chart(fig_pie, use_container_width=True)
            
            with col2:
                # Port usage chart
                ports = [instance['Port'] for instance in instances]
                statuses = [instance['Status'] for instance in instances]
                
                colors = ['green' if 'Running' in status else 'red' for status in statuses]
                
                fig_bar = go.Figure(data=[
                    go.Bar(x=ports, y=[1]*len(ports), marker_color=colors)
                ])
                fig_bar.update_layout(
                    title="Port Usage Status",
                    xaxis_title="Port Number",
                    yaxis_title="Status",
                    showlegend=False
                )
                st.plotly_chart(fig_bar, use_container_width=True)
            
            # Instance details table
            st.subheader("📋 Detailed Instance Information")
            df = pd.DataFrame(instances)
            st.dataframe(df, use_container_width=True)
        
        else:
            st.info("No instances to monitor. Create some instances first!")
    
    with tab4:
        st.header("⚙️ System Settings")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🔧 Default Settings")
            
            current_auto_nginx = manager.config["settings"].get("auto_nginx", True)
            current_auto_ssl = manager.config["settings"].get("auto_ssl", True)
            
            new_auto_nginx = st.checkbox("Auto-configure Nginx by default", value=current_auto_nginx)
            new_auto_ssl = st.checkbox("Auto-setup SSL by default", value=current_auto_ssl)
            
            if st.button("💾 Save Settings"):
                manager.config["settings"]["auto_nginx"] = new_auto_nginx
                manager.config["settings"]["auto_ssl"] = new_auto_ssl
                manager.save_config()
                st.success("Settings saved!")
        
        with col2:
            st.subheader("🧹 Maintenance")
            
            if st.button("🔄 Reload Nginx"):
                try:
                    import subprocess
                    subprocess.run(["sudo", "systemctl", "reload", "nginx"], check=True)
                    st.success("Nginx reloaded successfully!")
                except:
                    st.error("Failed to reload Nginx")
            
            if st.button("📋 Show SSL Certificates"):
                try:
                    import subprocess
                    result = subprocess.run(["sudo", "certbot", "certificates"], 
                                          capture_output=True, text=True)
                    st.code(result.stdout)
                except:
                    st.error("Failed to get SSL certificate information")
            
            if st.button("🧹 Clean Up Unused Configs"):
                st.info("This feature will be implemented in the next update")

else:
    st.error("Authentication required. Please log in to access the dashboard.")
