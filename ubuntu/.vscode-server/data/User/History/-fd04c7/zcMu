# Authentication
- User prefers to add username/password authentication to dashboards when they are exposed via public domains for security.
- User prefers persistent login sessions without repeated password prompts.

# Hosting and Infrastructure
- User hosts www.algofactory.in on Hostinger and wants to run AlgoFactory instances on subdomains with public IP **************.
- User prefers to run AlgoFactory instances on port 5000 and wants domain-based configuration in .env files for deployment.
- User prefers to initially deploy AlgoFactory on port 5000 with HTTP only (before HTTPS setup).
- User wants to run AlgoFactory clients on ports following the pattern 1010, 1012, etc., and dashboards on port 8502.
- For AlgoFactory instances, WebSocket ports should follow pattern 12000+port (e.g., port 1015 uses WebSocket 12015) and ZMQ ports should follow pattern 15000+port (e.g., port 1015 uses ZMQ 15015).
- User prefers websocket port 8765 to be used for internal purposes only, not exposed externally for security reasons.
- User prefers to backup git repositories to local PC for safety before making significant changes to server configurations.
- User prefers adding swap space as a solution for memory issues rather than upgrading server instances.
- User prefers dashboard and services to automatically restart after system reboots without manual intervention.
- User prefers automated nginx/SSL configuration for ports 1010-1020 when creating AlgoFactory instances via dashboard, with automatic cleanup on deletion, and automatic cleanup on deletion.
- User wants single-click instance start/stop management in the dashboard.
- User wants bulk start/stop buttons in dashboard.
- User expects stable connections without disconnection messages during instance creation.

# Development Environment
- User uses a shared virtual environment (shared-venv) for all Python projects and dependencies rather than global installations.
- User prefers to close all running applications (Streamlit, Flask/AlgoFactory) except VSCode when experiencing memory constraints to optimize server resources.
- User prefers to test services manually first before configuring auto-start functionality.

# AlgoFactory Instance Creation
- User prefers instance creation to not copy database folders (should be empty).