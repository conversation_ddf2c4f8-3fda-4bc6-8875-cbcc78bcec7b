2025-06-22 19:51:44.372 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-22 19:51:44.372 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-22 19:51:44.372 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryMaxChars":0,"historySummaryLowerChars":0,"historySummaryPrompt":"","enableSpawnSubAgentTool":false}
2025-06-22 19:51:44.372 [info] 'AugmentExtension' Retrieving model config
2025-06-22 19:51:45.329 [info] 'AugmentExtension' Retrieved model config
2025-06-22 19:51:45.329 [info] 'AugmentExtension' Returning model config
2025-06-22 19:51:45.454 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryMaxChars: 0 to 200000
  - historySummaryLowerChars: 0 to 80000
  - historySummaryPrompt: "" to "Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\n\nYour summary should be structured as follows:\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\n\nExample summary structure:\n1. Previous Conversation:\n[Detailed description]\n2. Current Work:\n[Detailed description]\n3. Key Technical Concepts:\n- [Concept 1]\n- [Concept 2]\n- [...]\n4. Relevant Files and Code:\n- [File Name 1]\n    - [Summary of why this file is important]\n    - [Summary of the changes made to this file, if any]\n    - [Important Code Snippet]\n- [File Name 2]\n    - [Important Code Snippet]\n- [...]\n5. Problem Solving:\n[Detailed description]\n6. Pending Tasks and Next Steps:\n- [Task 1 details & next steps]\n- [Task 2 details & next steps]\n- [...]\n\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\n"
2025-06-22 19:51:45.454 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 4:16:52 PM
2025-06-22 19:51:45.454 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [false]
2025-06-22 19:51:45.454 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-22 19:51:45.454 [info] 'SyncingPermissionTracker' Permission to sync folder /home granted at 6/22/2025, 4:16:52 PM; type = explicit
2025-06-22 19:51:45.454 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = granted
2025-06-22 19:51:45.454 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 4:16:52 PM
2025-06-22 19:51:45.506 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-22 19:51:45.506 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-22 19:51:45.512 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-22 19:51:45.552 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-22 19:51:45.554 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-22 19:51:46.476 [info] 'WorkspaceManager[home]' Start tracking
2025-06-22 19:51:46.501 [info] 'PathMap' Opened source folder /home with id 100
2025-06-22 19:51:46.501 [info] 'OpenFileManager' Opened source folder 100
2025-06-22 19:51:46.505 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/04fdc7e3b27821b32421fb99636aa0cb/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-22 19:51:47.135 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-22 19:51:47.135 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-22 19:51:47.535 [info] 'MtimeCache[home]' read 32836 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/04fdc7e3b27821b32421fb99636aa0cb/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-22 19:51:49.345 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-22 19:51:49.347 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-22 19:51:49.347 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-22 19:51:49.347 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-22 19:51:49.348 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2093 msec late.
2025-06-22 19:52:15.200 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-22 19:52:16.058 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-22 19:52:16.058 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-22 19:52:16.058 [info] 'TaskManager' Setting current root task UUID to f084d62a-11a9-499d-ad69-9bf0f0f51d64
2025-06-22 19:52:17.862 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-22 19:53:02.806 [info] 'WorkspaceManager[home]' Tracking enabled
2025-06-22 19:53:02.806 [info] 'WorkspaceManager[home]' Path metrics:
  - directories emitted: 7920
  - files emitted: 49651
  - other paths emitted: 4
  - total paths emitted: 57575
  - timing stats:
    - readDir: 714 ms
    - filter: 1834 ms
    - yield: 431 ms
    - total: 3766 ms
2025-06-22 19:53:02.807 [info] 'WorkspaceManager[home]' File metrics:
  - paths accepted: 32924
  - paths not accessible: 0
  - not plain files: 0
  - large files: 190
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 32827
  - mtime cache misses: 97
  - probe batches: 44
  - blob names probed: 32935
  - files read: 16688
  - blobs uploaded: 15
  - timing stats:
    - ingestPath: 127 ms
    - probe: 39993 ms
    - stat: 2525 ms
    - read: 25978 ms
    - upload: 3028 ms
2025-06-22 19:53:02.807 [info] 'WorkspaceManager[home]' Startup metrics:
  - create SourceFolder: 28 ms
  - read MtimeCache: 1033 ms
  - pre-populate PathMap: 1489 ms
  - create PathFilter: 5175 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 3769 ms
  - purge stale PathMap entries: 12 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 64783 ms
  - enable persist: 42 ms
  - total: 76331 ms
2025-06-22 19:53:02.807 [info] 'WorkspaceManager' Workspace startup complete in 77380 ms
2025-06-22 19:53:30.897 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/logs/20250622T195139/exthost1/vscode.json-language-features
2025-06-22 20:20:30.303 [info] 'ToolFileUtils' Reading file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf
2025-06-22 20:20:30.374 [info] 'ToolFileUtils' Successfully read file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf (4789 bytes)
2025-06-22 20:20:31.382 [info] 'WorkspaceManager[home]' Directory created: ubuntu/.vscode-server/data/User/History/71199c6d
2025-06-22 20:20:32.145 [info] 'ToolFileUtils' Reading file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf
2025-06-22 20:20:32.146 [info] 'ToolFileUtils' Successfully read file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf (4827 bytes)
2025-06-22 20:20:42.569 [info] 'ViewTool' Tool called with path: ubuntu/git/nginx-config/dashboard.algofactory.in.conf and view_range: [9,15]
2025-06-22 20:21:44.152 [info] 'AugmentExtension' Retrieving model config
2025-06-22 20:21:44.593 [info] 'AugmentExtension' Retrieved model config
2025-06-22 20:21:44.593 [info] 'AugmentExtension' Returning model config
2025-06-22 20:23:53.800 [info] 'ToolFileUtils' Reading file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf
2025-06-22 20:23:53.870 [info] 'ToolFileUtils' Successfully read file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf (4827 bytes)
2025-06-22 20:23:55.141 [info] 'ToolFileUtils' Reading file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf
2025-06-22 20:23:55.142 [info] 'ToolFileUtils' Successfully read file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf (5495 bytes)
2025-06-22 20:26:56.070 [error] 'AugmentExtension' API request 63c9b5df-0cf5-482f-a955-c8f162b3bc94 to https://d4.api.augmentcode.com/find-missing response 401: Unauthorized
2025-06-22 20:26:56.322 [info] 'activate()' ======== Reloading extension ========
2025-06-22 20:26:56.322 [info] 'PathMap' Closed source folder /home with id 100
2025-06-22 20:26:56.322 [info] 'OpenFileManager' Closed source folder 100
2025-06-22 20:26:56.384 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unknown" due to error: Please configure Augment API URL
2025-06-22 20:26:56.384 [error] 'ClientMetricsReporter' Error uploading metrics: Error: Please configure Augment API URL Error: Please configure Augment API URL
	at WQ.callApi (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:890:12485)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async WQ.callApi (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:890:58032)
	at async WQ.clientMetrics (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:890:52627)
	at async /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:661:14649
	at async Is (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:661:12861)
	at async e._doUpload (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:661:14548)
	at async /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:661:13871
2025-06-22 20:26:56.385 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): Please configure Augment API URL Error: Please configure Augment API URL
	at WQ.callApi (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:890:12485)
	at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
	at async WQ.callApi (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:890:58032)
	at async WQ.clientMetrics (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:890:52627)
	at async /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:661:14649
	at async Is (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:661:12861)
	at async e._doUpload (/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:661:14548)
	at async /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1/out/extension.js:661:13871
2025-06-22 20:26:56.434 [error] 'AugmentExtension' API request 1733f655-e857-45a2-8100-ef06fe81348e to https://d4.api.augmentcode.com/report-error response 401: Unauthorized
2025-06-22 20:26:56.434 [error] 'AugmentExtension' Dropping error report "find-missing call failed with APIStatus unauthenticated" due to error: HTTP error: 401 Unauthorized
2025-06-22 20:27:41.058 [info] 'OAuthFlow' Creating new session...
2025-06-22 20:27:41.103 [info] 'OAuthFlow' Opening URL: https://auth.augmentcode.com/authorize?response_type=code&code_challenge=zRZ8aExfbA1bcgOLBGJQ72yea9Xkdj8VMMeE6SsDBQE&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=vscode%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=b61881e1-e723-4910-97ed-ee1bffb9d0f5&scope=email&prompt=login
2025-06-22 20:28:08.732 [info] 'activate()' ======== Reloading extension ========
2025-06-22 20:28:08.745 [info] 'AugmentExtension' Retrieving model config
2025-06-22 20:28:08.745 [info] 'OAuthFlow' Created session https://i1.api.augmentcode.com/
2025-06-22 20:28:09.218 [info] 'AugmentExtension' Retrieved model config
2025-06-22 20:28:09.218 [info] 'AugmentExtension' Returning model config
2025-06-22 20:28:09.264 [info] 'FeatureFlagManager' feature flags changed:
  - enableHindsight: false to true
  - vscodeShareMinVersion: "" to "0.314.0"
  - openFileManagerV2Enabled: false to true
2025-06-22 20:28:09.265 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 4:16:52 PM
2025-06-22 20:28:09.265 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-22 20:28:09.265 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-22 20:28:09.265 [info] 'SyncingPermissionTracker' Permission to sync folder /home granted at 6/22/2025, 4:16:52 PM; type = explicit
2025-06-22 20:28:09.265 [info] 'WorkspaceManager' Adding workspace folder home; folderRoot = /home; syncingPermission = granted
2025-06-22 20:28:09.265 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home (explicit) at 6/22/2025, 4:16:52 PM
2025-06-22 20:28:09.339 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-22 20:28:09.339 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-22 20:28:09.339 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-22 20:28:09.346 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-22 20:28:09.378 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-22 20:28:09.378 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-22 20:28:09.702 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-22 20:28:09.702 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-22 20:28:09.915 [info] 'TaskManager' Setting current root task UUID to f084d62a-11a9-499d-ad69-9bf0f0f51d64
2025-06-22 20:28:10.038 [info] 'WorkspaceManager[home]' Start tracking
2025-06-22 20:28:10.055 [info] 'PathMap' Opened source folder /home with id 100
2025-06-22 20:28:10.055 [info] 'OpenFileManager' Opened source folder 100
2025-06-22 20:28:10.445 [info] 'MtimeCache[home]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/04fdc7e3b27821b32421fb99636aa0cb/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-22 20:28:10.528 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-22 20:28:10.528 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-22 20:28:10.625 [info] 'MtimeCache[home]' read 32862 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/04fdc7e3b27821b32421fb99636aa0cb/Augment.vscode-augment/2cc974af6afc822c42a4e914df05c697348b80420941f6b27561b2bf688587b7/mtime-cache.json
2025-06-22 20:28:12.103 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-22 20:28:12.103 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-22 20:28:12.104 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-22 20:28:12.104 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-06-22 20:28:12.384 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":2469.274745,"timestamp":"2025-06-22T14:58:12.379Z"}]
2025-06-22 20:28:12.589 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-06-22 20:28:23.858 [info] 'StallDetector' Recent work: [{"name":"resolve-file-request","durationMs":11509.006302,"timestamp":"2025-06-22T14:58:23.813Z"},{"name":"resolve-file-request","durationMs":11522.86278,"timestamp":"2025-06-22T14:58:23.827Z"}]
2025-06-22 20:30:01.237 [info] 'ToolFileUtils' Reading file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf
2025-06-22 20:30:01.237 [info] 'ToolFileUtils' Successfully read file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf (5495 bytes)
2025-06-22 20:30:02.548 [info] 'ToolFileUtils' Reading file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf
2025-06-22 20:30:02.548 [info] 'ToolFileUtils' Successfully read file: ubuntu/git/nginx-config/5000.algofactory.in-http.conf (5582 bytes)
2025-06-22 20:33:55.134 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2742 msec late.
