2025-06-22 19:51:44.113 [info] [main] Log level: Info
2025-06-22 19:51:44.113 [info] [main] Validating found git in: "git"
2025-06-22 19:51:44.113 [info] [main] Using git "2.43.0" from "git"
2025-06-22 19:51:44.113 [info] [Model][doInitialScan] Initial repository scan started
2025-06-22 19:51:44.113 [info] > git rev-parse --show-toplevel [33ms]
2025-06-22 19:51:44.113 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:51:44.113 [info] > git rev-parse --show-toplevel [31ms]
2025-06-22 19:51:44.113 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-22 19:51:44.113 [info] [Model][openRepository] Opened repository (path): /home/<USER>/git/algofactory
2025-06-22 19:51:44.113 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/git/algofactory
2025-06-22 19:51:44.113 [info] > git rev-parse --show-toplevel [977ms]
2025-06-22 19:51:44.113 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 19:51:44.113 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-22 19:51:44.113 [info] > git config --get commit.template [988ms]
2025-06-22 19:51:44.226 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [115ms]
2025-06-22 19:51:44.246 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:51:44.273 [info] > git rev-parse refs/remotes/origin/main [27ms]
2025-06-22 19:51:44.358 [info] > git show --textconv :.env [17ms]
2025-06-22 19:51:44.358 [info] > git ls-files --stage -- .env [8ms]
2025-06-22 19:51:44.370 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [55ms]
2025-06-22 19:51:44.392 [info] > git hash-object -t tree /dev/null [22ms]
2025-06-22 19:51:44.394 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/git/algofactory/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 19:51:44.394 [info] > git hash-object -t tree /dev/null [31ms]
2025-06-22 19:51:44.395 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/git/algofactory/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 19:51:44.657 [info] > git check-ignore -v -z --stdin [7ms]
2025-06-22 19:51:44.758 [info] > git status -z -uall [461ms]
2025-06-22 19:51:44.842 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-22 19:51:44.855 [info] > git config --get --local branch.main.vscode-merge-base [6ms]
2025-06-22 19:51:44.860 [info] > git config --get commit.template [18ms]
2025-06-22 19:51:44.879 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [19ms]
2025-06-22 19:51:45.030 [info] > git merge-base refs/heads/main refs/remotes/origin/main [144ms]
2025-06-22 19:51:45.038 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-06-22 19:51:45.043 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 19:51:45.043 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [9ms]
2025-06-22 19:51:45.046 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 19:51:45.061 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 19:51:45.096 [info] > git status -z -uall [46ms]
2025-06-22 19:51:45.675 [info] > git check-ignore -v -z --stdin [10ms]
2025-06-22 19:52:06.106 [info] > git ls-files --stage -- .env [6ms]
2025-06-22 19:52:06.107 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/git/algofactory/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 19:52:06.111 [info] > git show --textconv :.env [14ms]
2025-06-22 19:52:06.111 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/git/algofactory/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 20:13:30.314 [info] > git config --get commit.template [29ms]
2025-06-22 20:13:30.324 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [27ms]
2025-06-22 20:13:30.334 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:13:30.336 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 20:13:30.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-22 20:13:30.830 [info] > git status -z -uall [486ms]
2025-06-22 20:15:27.033 [info] > git config --get commit.template [2ms]
2025-06-22 20:15:27.041 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-22 20:15:27.044 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:15:27.046 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 20:15:27.063 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 20:15:27.214 [info] > git status -z -uall [162ms]
2025-06-22 20:16:07.111 [info] > git config --get commit.template [8ms]
2025-06-22 20:16:07.119 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-22 20:16:07.123 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:16:07.124 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 20:16:07.137 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 20:16:07.611 [info] > git status -z -uall [481ms]
2025-06-22 20:21:26.894 [info] > git config --get commit.template [26ms]
2025-06-22 20:21:26.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [30ms]
2025-06-22 20:21:26.914 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:21:26.916 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 20:21:26.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-22 20:21:27.418 [info] > git status -z -uall [495ms]
2025-06-22 20:21:42.986 [info] > git config --get commit.template [0ms]
2025-06-22 20:21:42.992 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 20:21:42.995 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:21:42.997 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 20:21:43.010 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 20:21:43.039 [info] > git status -z -uall [38ms]
2025-06-22 20:22:16.012 [info] > git config --get commit.template [1ms]
2025-06-22 20:22:16.018 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 20:22:16.022 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:22:16.022 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 20:22:16.039 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 20:22:16.064 [info] > git status -z -uall [37ms]
2025-06-22 20:22:21.096 [info] > git config --get commit.template [6ms]
2025-06-22 20:22:21.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 20:22:21.102 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:22:21.103 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 20:22:21.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-22 20:22:21.144 [info] > git status -z -uall [37ms]
2025-06-22 20:22:57.239 [info] > git config --get commit.template [1ms]
2025-06-22 20:22:57.245 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 20:22:57.249 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:22:57.250 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 20:22:57.263 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-22 20:22:57.289 [info] > git status -z -uall [35ms]
2025-06-22 20:23:02.308 [info] > git config --get commit.template [1ms]
2025-06-22 20:23:02.314 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 20:23:02.319 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:23:02.319 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 20:23:02.333 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 20:23:02.776 [info] > git status -z -uall [453ms]
2025-06-22 20:31:51.375 [info] > git config --get commit.template [31ms]
2025-06-22 20:31:51.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [37ms]
2025-06-22 20:31:51.390 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:31:51.395 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-22 20:31:51.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-22 20:31:51.897 [info] > git status -z -uall [491ms]
2025-06-22 20:33:38.083 [info] > git config --get commit.template [42ms]
2025-06-22 20:33:38.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [51ms]
2025-06-22 20:33:38.103 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:33:38.106 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 20:33:38.140 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-22 20:33:38.544 [info] > git status -z -uall [428ms]
2025-06-22 20:33:43.721 [info] > git config --get commit.template [29ms]
2025-06-22 20:33:43.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [116ms]
2025-06-22 20:33:43.855 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:33:43.858 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 20:33:43.892 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [23ms]
2025-06-22 20:33:44.281 [info] > git status -z -uall [418ms]
2025-06-22 20:34:12.069 [info] > git config --get commit.template [1120ms]
2025-06-22 20:34:12.230 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [42ms]
2025-06-22 20:34:12.261 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:34:16.047 [info] > git rev-parse refs/remotes/origin/main [3787ms]
2025-06-22 20:34:16.171 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [97ms]
2025-06-22 20:34:16.731 [info] > git status -z -uall [671ms]
2025-06-22 20:34:22.577 [info] > git config --get commit.template [607ms]
2025-06-22 20:34:22.791 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [198ms]
2025-06-22 20:34:22.805 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 20:34:22.823 [info] > git rev-parse refs/remotes/origin/main [18ms]
2025-06-22 20:34:22.920 [info] > git status -z -uall [79ms]
2025-06-22 20:34:22.920 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-06-22 20:53:47.493 [info] > git check-ignore -v -z --stdin [14ms]
2025-06-22 20:58:51.115 [info] > git rev-parse --show-toplevel [0ms]
2025-06-22 20:58:51.128 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-22 20:58:51.178 [info] [Model][openRepository] Opened repository (path): /home/<USER>/git/openalgo
2025-06-22 20:58:51.178 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/git/openalgo
2025-06-22 20:58:51.192 [info] > git config --get commit.template [1ms]
2025-06-22 20:58:51.229 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [29ms]
2025-06-22 20:58:51.257 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-22 20:58:51.772 [info] > git status -z -uall [534ms]
2025-06-22 20:58:51.830 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-22 20:58:51.832 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-06-22 20:58:51.842 [info] > git config --get commit.template [10ms]
2025-06-22 20:58:51.845 [info] > git config --get --local branch.main.vscode-merge-base [6ms]
2025-06-22 20:58:51.861 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [8ms]
2025-06-22 20:58:51.868 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-22 20:58:51.979 [info] > git status -z -uall [104ms]
2025-06-22 20:58:51.979 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [94ms]
2025-06-22 20:58:52.019 [info] > git merge-base refs/heads/main refs/remotes/origin/main [152ms]
2025-06-22 20:58:52.031 [info] > git merge-base refs/heads/main refs/remotes/origin/main [15ms]
2025-06-22 20:58:52.038 [info] > git diff --name-status -z --diff-filter=ADMR 187a66e4c9f9978396adc75b3bca6d123322e50a...refs/remotes/origin/main [13ms]
2025-06-22 20:58:52.041 [info] > git diff --name-status -z --diff-filter=ADMR 187a66e4c9f9978396adc75b3bca6d123322e50a...refs/remotes/origin/main [3ms]
2025-06-22 20:59:59.778 [info] > git config --get commit.template [1ms]
2025-06-22 20:59:59.849 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [67ms]
2025-06-22 20:59:59.876 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-22 20:59:59.902 [info] > git status -z -uall [44ms]
2025-06-22 21:02:17.308 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 21:02:19.542 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 21:11:36.072 [info] > git rev-parse --show-toplevel [28ms]
2025-06-22 21:11:36.084 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-22 21:11:36.123 [info] [Model][openRepository] Opened repository (path): /home/<USER>/git/algofactory-1015
2025-06-22 21:11:36.123 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/git/algofactory-1015
2025-06-22 21:11:36.155 [info] > git config --get commit.template [13ms]
2025-06-22 21:11:36.156 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-22 21:11:36.162 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:11:36.163 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:11:36.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 21:11:36.224 [info] > git status -z -uall [53ms]
2025-06-22 21:11:36.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-22 21:11:36.288 [info] > git config --get commit.template [11ms]
2025-06-22 21:11:36.293 [info] > git config --get --local branch.main.vscode-merge-base [7ms]
2025-06-22 21:11:36.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-22 21:11:36.305 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:11:36.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [7ms]
2025-06-22 21:11:36.314 [info] > git rev-parse refs/remotes/origin/main [9ms]
2025-06-22 21:11:36.417 [info] > git status -z -uall [98ms]
2025-06-22 21:11:36.417 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [88ms]
2025-06-22 21:11:36.444 [info] > git merge-base refs/heads/main refs/remotes/origin/main [132ms]
2025-06-22 21:11:36.455 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [5ms]
2025-06-22 21:11:36.456 [info] > git merge-base refs/heads/main refs/remotes/origin/main [13ms]
2025-06-22 21:11:36.465 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [3ms]
2025-06-22 21:11:36.792 [info] > git check-ignore -v -z --stdin [12ms]
2025-06-22 21:11:36.793 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 21:11:36.795 [info] > git check-ignore -v -z --stdin [9ms]
2025-06-22 21:11:49.062 [info] > git check-ignore -v -z --stdin [17ms]
2025-06-22 21:11:51.724 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-22 21:11:52.762 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-22 21:11:59.440 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 21:12:00.303 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-22 21:12:01.650 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-22 21:12:02.570 [info] > git show --textconv :.env [13ms]
2025-06-22 21:12:02.582 [info] > git ls-files --stage -- .env [17ms]
2025-06-22 21:12:02.593 [info] > git hash-object -t tree /dev/null [2ms]
2025-06-22 21:12:02.593 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/git/algofactory-1015/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory-1015%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 21:12:02.597 [info] > git hash-object -t tree /dev/null [17ms]
2025-06-22 21:12:02.597 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/git/algofactory-1015/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory-1015%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 21:12:27.185 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 21:15:09.622 [info] > git rev-parse --show-toplevel [29ms]
2025-06-22 21:15:09.623 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 21:17:44.037 [info] > git config --get commit.template [14ms]
2025-06-22 21:17:44.058 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-06-22 21:17:44.067 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:17:44.069 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 21:17:44.101 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-22 21:17:44.672 [info] > git status -z -uall [593ms]
2025-06-22 21:22:44.433 [info] > git config --get commit.template [8ms]
2025-06-22 21:22:44.451 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-06-22 21:22:44.459 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory/.git/refs/remotes/origin/main'
2025-06-22 21:22:44.461 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 21:22:44.489 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-22 21:22:44.949 [info] > git status -z -uall [479ms]
2025-06-22 21:24:54.765 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/HEAD'
2025-06-22 21:24:54.781 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/config'
2025-06-22 21:24:54.831 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/HEAD'
2025-06-22 21:24:54.838 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/config'
2025-06-22 21:24:55.540 [info] > git check-ignore -v -z --stdin [36ms]
2025-06-22 21:24:55.543 [info] > git check-ignore -v -z --stdin [45ms]
2025-06-22 21:25:33.209 [info] > git rev-parse --show-toplevel [80ms]
2025-06-22 21:25:33.225 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-22 21:25:33.257 [info] [Model][openRepository] Opened repository (path): /home/<USER>/git/algofactory-1015
2025-06-22 21:25:33.258 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/git/algofactory-1015
2025-06-22 21:25:33.268 [info] > git config --get commit.template [0ms]
2025-06-22 21:25:33.278 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-22 21:25:33.283 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:25:33.285 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 21:25:33.312 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 21:25:33.365 [info] > git status -z -uall [72ms]
2025-06-22 21:25:33.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-06-22 21:25:33.431 [info] > git config --get --local branch.main.vscode-merge-base [1ms]
2025-06-22 21:25:33.439 [info] > git config --get commit.template [25ms]
2025-06-22 21:25:33.440 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [2ms]
2025-06-22 21:25:33.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-22 21:25:33.456 [info] > git merge-base refs/heads/main refs/remotes/origin/main [9ms]
2025-06-22 21:25:33.466 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:25:33.470 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 21:25:33.494 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [33ms]
2025-06-22 21:25:33.598 [info] > git status -z -uall [119ms]
2025-06-22 21:25:33.599 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [105ms]
2025-06-22 21:25:33.941 [info] > git check-ignore -v -z --stdin [15ms]
2025-06-22 21:25:33.941 [info] > git check-ignore -v -z --stdin [6ms]
2025-06-22 21:25:33.946 [info] > git check-ignore -v -z --stdin [7ms]
2025-06-22 21:30:14.005 [info] > git config --get commit.template [34ms]
2025-06-22 21:30:14.022 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [45ms]
2025-06-22 21:30:14.031 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:30:14.034 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 21:30:14.058 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-22 21:30:14.567 [info] > git status -z -uall [524ms]
2025-06-22 21:30:24.648 [info] > git check-ignore -v -z --stdin [5ms]
2025-06-22 21:30:25.480 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 21:30:26.691 [info] > git check-ignore -v -z --stdin [8ms]
2025-06-22 21:30:27.494 [info] > git show --textconv :.env [8ms]
2025-06-22 21:30:27.494 [info] > git ls-files --stage -- .env [1ms]
2025-06-22 21:30:27.507 [info] > git hash-object -t tree /dev/null [7ms]
2025-06-22 21:30:27.507 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/git/algofactory-1015/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory-1015%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 21:30:27.512 [info] > git hash-object -t tree /dev/null [6ms]
2025-06-22 21:30:27.512 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/git/algofactory-1015/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory-1015%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 21:35:28.014 [info] > git config --get commit.template [25ms]
2025-06-22 21:35:28.024 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [47ms]
2025-06-22 21:35:28.033 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:35:28.036 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 21:35:28.060 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-22 21:35:28.502 [info] > git status -z -uall [459ms]
2025-06-22 21:36:50.211 [info] > git config --get commit.template [8ms]
2025-06-22 21:36:50.217 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-22 21:36:50.223 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:36:50.225 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 21:36:50.249 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-22 21:36:50.741 [info] > git status -z -uall [510ms]
2025-06-22 21:37:16.711 [info] > git config --get commit.template [37ms]
2025-06-22 21:37:16.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [43ms]
2025-06-22 21:37:16.733 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:37:16.734 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:37:16.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 21:37:17.195 [info] > git status -z -uall [454ms]
2025-06-22 21:37:22.230 [info] > git config --get commit.template [6ms]
2025-06-22 21:37:22.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-22 21:37:22.237 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:37:22.238 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:37:22.258 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 21:37:22.280 [info] > git status -z -uall [37ms]
2025-06-22 21:40:17.485 [info] > git config --get commit.template [34ms]
2025-06-22 21:40:17.491 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [47ms]
2025-06-22 21:40:17.499 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:40:17.502 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 21:40:17.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-22 21:40:17.981 [info] > git status -z -uall [472ms]
2025-06-22 21:40:23.040 [info] > git config --get commit.template [7ms]
2025-06-22 21:40:23.041 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 21:40:23.047 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:40:23.050 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 21:40:23.067 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 21:40:23.418 [info] > git status -z -uall [365ms]
2025-06-22 21:41:53.478 [info] > git config --get commit.template [36ms]
2025-06-22 21:41:53.481 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [50ms]
2025-06-22 21:41:53.489 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:41:53.490 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 21:41:53.510 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-22 21:41:53.997 [info] > git status -z -uall [500ms]
2025-06-22 21:42:04.557 [info] > git config --get commit.template [9ms]
2025-06-22 21:42:04.557 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-22 21:42:04.564 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:42:04.564 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 21:42:04.583 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-22 21:42:04.942 [info] > git status -z -uall [371ms]
2025-06-22 21:42:25.311 [info] > git config --get commit.template [6ms]
2025-06-22 21:42:25.318 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-22 21:42:25.324 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:42:25.325 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:42:25.338 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 21:42:25.792 [info] > git status -z -uall [461ms]
2025-06-22 21:42:33.299 [info] > git check-ignore -v -z --stdin [34ms]
2025-06-22 21:42:44.598 [info] > git config --get commit.template [39ms]
2025-06-22 21:42:44.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [35ms]
2025-06-22 21:42:44.630 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:42:44.634 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 21:42:44.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-22 21:42:44.707 [info] > git status -z -uall [66ms]
2025-06-22 21:43:27.887 [info] > git config --get commit.template [4ms]
2025-06-22 21:43:27.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-22 21:43:27.917 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:43:27.918 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 21:43:27.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 21:43:28.396 [info] > git status -z -uall [473ms]
2025-06-22 21:43:33.853 [info] > git config --get commit.template [179ms]
2025-06-22 21:43:33.870 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-22 21:43:33.879 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:43:33.901 [info] > git rev-parse refs/remotes/origin/main [22ms]
2025-06-22 21:43:34.009 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [93ms]
2025-06-22 21:43:34.421 [info] > git status -z -uall [512ms]
2025-06-22 21:44:04.771 [info] > git config --get commit.template [19ms]
2025-06-22 21:44:04.775 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-22 21:44:04.791 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:44:04.798 [info] > git rev-parse refs/remotes/origin/main [7ms]
2025-06-22 21:44:04.816 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 21:44:05.203 [info] > git status -z -uall [399ms]
2025-06-22 21:44:10.243 [info] > git config --get commit.template [5ms]
2025-06-22 21:44:10.243 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-22 21:44:10.250 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:44:10.250 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 21:44:10.269 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 21:44:10.293 [info] > git status -z -uall [38ms]
2025-06-22 21:46:20.940 [info] > git config --get commit.template [8ms]
2025-06-22 21:46:20.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 21:46:20.946 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:46:20.947 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:46:20.959 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-22 21:46:21.309 [info] > git status -z -uall [356ms]
2025-06-22 21:46:51.953 [info] > git config --get commit.template [0ms]
2025-06-22 21:46:51.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 21:46:51.966 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 21:46:51.967 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 21:46:51.985 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 21:46:52.009 [info] > git status -z -uall [37ms]
2025-06-22 22:07:11.144 [info] > git config --get commit.template [8ms]
2025-06-22 22:07:11.145 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 22:07:11.150 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 22:07:11.153 [info] > git rev-parse refs/remotes/origin/main [3ms]
2025-06-22 22:07:11.175 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-06-22 22:07:11.298 [info] > git status -z -uall [141ms]
2025-06-22 22:07:16.344 [info] > git config --get commit.template [9ms]
2025-06-22 22:07:16.345 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-22 22:07:16.350 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 22:07:16.351 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 22:07:16.370 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 22:07:16.397 [info] > git status -z -uall [40ms]
2025-06-22 22:07:31.818 [info] > git config --get commit.template [16ms]
2025-06-22 22:07:31.821 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-22 22:07:31.834 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 22:07:31.842 [info] > git rev-parse refs/remotes/origin/main [8ms]
2025-06-22 22:07:31.911 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [19ms]
2025-06-22 22:07:31.944 [info] > git status -z -uall [87ms]
2025-06-22 22:08:20.169 [info] > git config --get commit.template [5ms]
2025-06-22 22:08:20.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 22:08:20.185 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 22:08:20.186 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 22:08:20.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 22:08:20.233 [info] > git status -z -uall [41ms]
2025-06-22 22:08:25.256 [info] > git config --get commit.template [0ms]
2025-06-22 22:08:25.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 22:08:25.267 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/refs/remotes/origin/main'
2025-06-22 22:08:25.268 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 22:08:25.287 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 22:08:25.312 [info] > git status -z -uall [39ms]
2025-06-22 22:08:48.299 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/HEAD'
2025-06-22 22:08:48.316 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/config'
2025-06-22 22:08:48.351 [warning] [Git][getHEAD] Failed to parse HEAD file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/HEAD'
2025-06-22 22:08:48.360 [warning] [Git][getRemotes] Error: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1015/.git/config'
2025-06-22 22:08:49.051 [info] > git check-ignore -v -z --stdin [15ms]
2025-06-22 22:08:49.051 [info] > git check-ignore -v -z --stdin [9ms]
2025-06-22 22:09:19.648 [info] > git rev-parse --show-toplevel [2ms]
2025-06-22 22:09:19.658 [info] > git rev-parse --git-dir --git-common-dir [2ms]
2025-06-22 22:09:19.681 [info] [Model][openRepository] Opened repository (path): /home/<USER>/git/algofactory-1013
2025-06-22 22:09:19.681 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/git/algofactory-1013
2025-06-22 22:09:19.690 [info] > git config --get commit.template [0ms]
2025-06-22 22:09:19.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-22 22:09:19.708 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:09:19.882 [info] > git rev-parse refs/remotes/origin/main [174ms]
2025-06-22 22:09:19.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-22 22:09:20.011 [info] > git status -z -uall [116ms]
2025-06-22 22:09:20.055 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-22 22:09:20.064 [info] > git config --get --local branch.main.vscode-merge-base [1ms]
2025-06-22 22:09:20.069 [info] > git config --get commit.template [16ms]
2025-06-22 22:09:20.121 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [52ms]
2025-06-22 22:09:20.183 [info] > git merge-base refs/heads/main refs/remotes/origin/main [56ms]
2025-06-22 22:09:20.191 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [73ms]
2025-06-22 22:09:20.198 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:09:20.202 [info] > git diff --name-status -z --diff-filter=ADMR 89ac1fedef89963fb80f591849bbcbe1dc91b13e...refs/remotes/origin/main [11ms]
2025-06-22 22:09:20.203 [info] > git rev-parse refs/remotes/origin/main [5ms]
2025-06-22 22:09:20.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-22 22:09:20.260 [info] > git status -z -uall [53ms]
2025-06-22 22:09:20.422 [info] > git check-ignore -v -z --stdin [16ms]
2025-06-22 22:09:20.423 [info] > git check-ignore -v -z --stdin [10ms]
2025-06-22 22:09:20.423 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-22 22:09:58.391 [info] > git config --get commit.template [35ms]
2025-06-22 22:09:58.399 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [36ms]
2025-06-22 22:09:58.406 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:09:58.410 [info] > git rev-parse refs/remotes/origin/main [4ms]
2025-06-22 22:09:58.438 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-06-22 22:09:59.081 [info] > git status -z -uall [665ms]
2025-06-22 22:10:26.271 [info] > git config --get commit.template [8ms]
2025-06-22 22:10:26.276 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-22 22:10:26.283 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:10:26.283 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 22:10:26.301 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 22:10:26.761 [info] > git status -z -uall [473ms]
2025-06-22 22:10:29.851 [info] > git check-ignore -v -z --stdin [4ms]
2025-06-22 22:10:31.743 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-22 22:10:31.783 [info] > git config --get commit.template [6ms]
2025-06-22 22:10:31.785 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 22:10:31.789 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:10:31.791 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 22:10:31.812 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 22:10:31.836 [info] > git status -z -uall [39ms]
2025-06-22 22:10:40.851 [info] > git config --get commit.template [7ms]
2025-06-22 22:10:40.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 22:10:40.859 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:10:40.859 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 22:10:40.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-22 22:10:41.286 [info] > git status -z -uall [422ms]
2025-06-22 22:10:46.309 [info] > git config --get commit.template [6ms]
2025-06-22 22:10:46.310 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 22:10:46.316 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:10:46.317 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 22:10:46.343 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 22:10:46.361 [info] > git status -z -uall [38ms]
2025-06-22 22:10:52.976 [info] > git config --get commit.template [0ms]
2025-06-22 22:10:52.983 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 22:10:52.989 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:10:52.989 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 22:10:53.007 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 22:10:53.030 [info] > git status -z -uall [37ms]
2025-06-22 22:10:54.190 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-22 22:10:55.821 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 22:10:57.492 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-22 22:10:58.049 [info] > git config --get commit.template [3ms]
2025-06-22 22:10:58.057 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 22:10:58.062 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:10:58.063 [info] > git rev-parse refs/remotes/origin/main [1ms]
2025-06-22 22:10:58.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 22:10:58.108 [info] > git status -z -uall [40ms]
2025-06-22 22:10:58.570 [info] > git show --textconv :.env [8ms]
2025-06-22 22:10:58.576 [info] > git ls-files --stage -- .env [7ms]
2025-06-22 22:10:58.585 [info] > git hash-object -t tree /dev/null [1ms]
2025-06-22 22:10:58.585 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/git/algofactory-1013/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory-1013%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 22:10:58.586 [info] > git hash-object -t tree /dev/null [11ms]
2025-06-22 22:10:58.586 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/git/algofactory-1013/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory-1013%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 22:11:13.212 [info] > git config --get commit.template [9ms]
2025-06-22 22:11:13.213 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-22 22:11:13.219 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:11:13.219 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 22:11:13.238 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-22 22:11:13.261 [info] > git status -z -uall [36ms]
2025-06-22 22:11:27.321 [info] > git config --get commit.template [10ms]
2025-06-22 22:11:27.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-22 22:11:27.327 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:11:27.328 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 22:11:27.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-22 22:11:27.377 [info] > git status -z -uall [44ms]
2025-06-22 22:11:32.401 [info] > git config --get commit.template [7ms]
2025-06-22 22:11:32.402 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-22 22:11:32.408 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:11:32.410 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 22:11:32.430 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 22:11:32.451 [info] > git status -z -uall [35ms]
2025-06-22 22:11:37.490 [info] > git config --get commit.template [7ms]
2025-06-22 22:11:37.492 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 22:11:37.497 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:11:37.499 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 22:11:37.519 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-22 22:11:37.543 [info] > git status -z -uall [38ms]
2025-06-22 22:11:59.035 [info] > git config --get commit.template [7ms]
2025-06-22 22:11:59.038 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-22 22:11:59.045 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:11:59.052 [info] > git rev-parse refs/remotes/origin/main [7ms]
2025-06-22 22:11:59.136 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [17ms]
2025-06-22 22:11:59.248 [info] > git status -z -uall [176ms]
2025-06-22 22:15:38.920 [info] > git rev-parse --show-toplevel [1ms]
2025-06-22 22:15:38.920 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 22:16:57.849 [info] > git config --get commit.template [71ms]
2025-06-22 22:16:57.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-22 22:16:57.878 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:16:57.880 [info] > git rev-parse refs/remotes/origin/main [2ms]
2025-06-22 22:16:57.899 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-22 22:16:58.352 [info] > git status -z -uall [466ms]
2025-06-22 22:17:03.383 [info] > git config --get commit.template [6ms]
2025-06-22 22:17:03.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-22 22:17:03.392 [warning] [Git][revParse] Unable to read file: ENOENT: no such file or directory, open '/home/<USER>/git/algofactory-1013/.git/refs/remotes/origin/main'
2025-06-22 22:17:03.392 [info] > git rev-parse refs/remotes/origin/main [0ms]
2025-06-22 22:17:03.411 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-22 22:17:03.437 [info] > git status -z -uall [38ms]
2025-06-22 22:17:13.634 [info] > git check-ignore -v -z --stdin [7ms]
2025-06-22 22:56:06.191 [info] > git ls-files --stage -- .env [7ms]
2025-06-22 22:56:06.196 [warning] [GitFileSystemProvider][stat] File not found - git:/home/<USER>/git/algofactory/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 22:56:06.199 [info] > git show --textconv :.env [27ms]
2025-06-22 22:56:06.199 [warning] [GitFileSystemProvider][readFile] File not found - git:/home/<USER>/git/algofactory/.env.git?%7B%22path%22%3A%22%2Fhome%2Fubuntu%2Fgit%2Falgofactory%2F.env%22%2C%22ref%22%3A%22%22%7D
2025-06-22 22:56:06.449 [info] > git rev-parse --show-toplevel [3ms]
2025-06-22 22:56:06.449 [info] fatal: not a git repository (or any of the parent directories): .git
2025-06-22 22:56:07.072 [info] > git check-ignore -v -z --stdin [3ms]
2025-06-22 22:56:59.202 [info] > git check-ignore -v -z --stdin [2ms]
