{% extends "layout.html" %}

{% block title %}Setup AlgoFactory Account{% endblock %}

{% block head %}
<style>
    .requirement-item {
        @apply flex items-center gap-2 text-sm py-1;
    }

    .requirement-met {
        @apply text-success;
    }

    .requirement-met svg {
        @apply opacity-100;
    }

    .requirement-not-met {
        @apply text-base-content/60;
    }

    .requirement-not-met svg {
        @apply opacity-0;
    }

    .password-strength-text {
        @apply text-xs mt-1 font-medium;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirm_password');
        const passwordMatchMessage = document.getElementById('passwordMatchMessage');
        const submitButton = document.getElementById('submitButton');
        const strengthMeter = document.getElementById('strengthMeter');
        const strengthText = document.getElementById('strengthText');
        const requirements = {
            length: document.getElementById('req-length'),
            uppercase: document.getElementById('req-uppercase'),
            lowercase: document.getElementById('req-lowercase'),
            number: document.getElementById('req-number'),
            special: document.getElementById('req-special')
        };

        function calculatePasswordStrength(password) {
            let score = 0;
            
            if (password.length >= 8) score += 20;
            if (password.length >= 12) score += 10;
            if (password.length >= 16) score += 10;
            if (/[A-Z]/.test(password)) score += 15;
            if (/[a-z]/.test(password)) score += 15;
            if (/[0-9]/.test(password)) score += 15;
            if (/[!@#$%^&*]/.test(password)) score += 15;
            
            return score;
        }

        function updateStrengthMeter(password) {
            const score = calculatePasswordStrength(password);
            let strength, className;

            if (score >= 80) {
                strength = 'Strong';
                className = 'progress-success';
            } else if (score >= 50) {
                strength = 'Medium';
                className = 'progress-warning';
            } else if (score > 0) {
                strength = 'Weak';
                className = 'progress-error';
            } else {
                strength = '';
                className = '';
            }

            strengthMeter.className = `progress ${className}`;
            strengthMeter.value = score;
            strengthText.textContent = strength;
            strengthText.className = `password-strength-text ${className ? 'text-' + className.replace('progress-', '') : ''}`;
        }

        function checkPasswordRequirements(password) {
            const checks = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /[0-9]/.test(password),
                special: /[!@#$%^&*]/.test(password)
            };

            Object.keys(checks).forEach(req => {
                const element = requirements[req];
                if (checks[req]) {
                    element.classList.remove('requirement-not-met');
                    element.classList.add('requirement-met');
                } else {
                    element.classList.remove('requirement-met');
                    element.classList.add('requirement-not-met');
                }
            });

            return Object.values(checks).every(Boolean);
        }

        function validateForm() {
            const passwordsMatch = password.value === confirmPassword.value;
            const meetsRequirements = checkPasswordRequirements(password.value);
            const allFieldsFilled = Array.from(document.querySelectorAll('input[required]'))
                .every(input => input.value.trim() !== '');

            if (passwordsMatch && meetsRequirements && allFieldsFilled) {
                submitButton.disabled = false;
                passwordMatchMessage.textContent = 'Passwords match';
                passwordMatchMessage.className = 'label-text-alt mt-1 text-success';
            } else {
                submitButton.disabled = true;
                if (!passwordsMatch && confirmPassword.value) {
                    passwordMatchMessage.textContent = 'Passwords do not match';
                    passwordMatchMessage.className = 'label-text-alt mt-1 text-error';
                } else if (!meetsRequirements) {
                    passwordMatchMessage.textContent = 'Password does not meet requirements';
                    passwordMatchMessage.className = 'label-text-alt mt-1 text-warning';
                } else {
                    passwordMatchMessage.textContent = '';
                }
            }
        }

        password.addEventListener('input', function() {
            validateForm();
            updateStrengthMeter(this.value);
        });
        confirmPassword.addEventListener('input', validateForm);
        document.querySelectorAll('input[required]').forEach(input => {
            input.addEventListener('input', validateForm);
        });

        document.getElementById('setupForm').addEventListener('submit', function(event) {
            if (!checkPasswordRequirements(password.value) || password.value !== confirmPassword.value) {
                event.preventDefault();
                validateForm();
            }
        });
    });
</script>
{% endblock %}

{% block content %}
<div class="min-h-[calc(100vh-8rem)] flex items-center justify-center">
    <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row items-center justify-between gap-16">
            <!-- Left side content -->
            <div class="flex-1 max-w-xl">
                <h1 class="text-5xl font-bold mb-6">Initial <span class="text-primary">Setup</span></h1>
                <p class="text-xl mb-8 opacity-80">
                    Welcome to AlgoFactory! Create your administrator account to get started with algorithmic trading. This account will have full access to manage the platform.
                </p>
                <div class="alert alert-info shadow-lg">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <h3 class="font-bold">First Time Setup</h3>
                        <div class="text-sm">This form will create the initial administrator account. You'll receive a TOTP QR code for password resets after setup.</div>
                    </div>
                </div>
            </div>

            <!-- Right side setup form -->
            <div class="card flex-shrink-0 w-full max-w-lg shadow-2xl bg-base-100">
                <div class="card-body">
                    <form id="setupForm" action="{{ url_for('core_bp.setup') }}" method="post" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <!-- Username -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Username</span>
                            </label>
                            <input type="text"
                                   id="username"
                                   name="username"
                                   required
                                   class="input input-bordered"
                                   placeholder="Choose a username"
                                   autocomplete="username">
                        </div>

                        <!-- Email -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Email</span>
                            </label>
                            <input type="email" 
                                   id="email" 
                                   name="email" 
                                   required 
                                   class="input input-bordered" 
                                   placeholder="Enter your email">
                        </div>

                        <!-- Password -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Password</span>
                            </label>
                            <input type="password" 
                                   id="password" 
                                   name="password" 
                                   required 
                                   class="input input-bordered w-full" 
                                   placeholder="Choose a password"
                                   autocomplete="new-password">
                            
                            <!-- Password Strength Meter -->
                            <progress id="strengthMeter" class="progress w-full mt-2" value="0" max="100"></progress>
                            <div id="strengthText" class="password-strength-text"></div>
                        </div>

                        <!-- Confirm Password -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">Confirm Password</span>
                            </label>
                            <input type="password" 
                                   id="confirm_password" 
                                   name="confirm_password" 
                                   required 
                                   class="input input-bordered" 
                                   placeholder="Confirm your password"
                                   autocomplete="new-password">
                            <div id="passwordMatchMessage" class="label-text-alt mt-1"></div>
                        </div>

                        <!-- Password Requirements -->
                        <div class="space-y-1">
                            <div id="req-length" class="requirement-item requirement-not-met">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>Minimum 8 characters</span>
                            </div>
                            <div id="req-uppercase" class="requirement-item requirement-not-met">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>At least 1 uppercase letter (A-Z)</span>
                            </div>
                            <div id="req-lowercase" class="requirement-item requirement-not-met">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>At least 1 lowercase letter (a-z)</span>
                            </div>
                            <div id="req-number" class="requirement-item requirement-not-met">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>At least 1 number (0-9)</span>
                            </div>
                            <div id="req-special" class="requirement-item requirement-not-met">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span>At least 1 special character (!@#$%^&*)</span>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-control mt-6">
                            <button type="submit" class="btn btn-primary w-full" id="submitButton" disabled>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Create Account
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
