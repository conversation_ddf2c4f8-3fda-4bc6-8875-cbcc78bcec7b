<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Add blocking script to set theme before any content loads -->
    <script>
        (function() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                document.documentElement.setAttribute('data-theme', savedTheme);
            }
        })();
    </script>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AlgoFactory is your premier open source algorithmic trading platform, offering a robust and secure environment for developing and executing trading strategies.">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>AlgoFactory</title>

    <!-- Favicon and touch icons -->
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon/favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='favicon/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon/favicon-16x16.png') }}">
    <link rel="manifest" href="{{ url_for('static', filename='favicon/site.webmanifest') }}">
    <link rel="mask-icon" href="{{ url_for('static', filename='favicon/safari-pinned-tab.svg') }}" color="#5bbad5">
    
    <!-- Compiled Tailwind CSS with DaisyUI -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    
    <!-- Audio for alerts -->
    <audio id="alert-sound" src="{{ url_for('static', filename='sounds/alert.mp3') }}" preload="auto"></audio>

    <!-- Meta tags for color settings -->
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">

    <!-- Theme and Mode Scripts -->
    <script src="{{ url_for('static', filename='js/theme.js') }}"></script>
    <script src="{{ url_for('static', filename='js/mode-toggle.js') }}"></script>

    <!-- CSRF Token Helper -->
    <script>
        function getCSRFToken() {
            const token = document.querySelector('meta[name="csrf-token"]');
            return token ? token.getAttribute('content') : '';
        }
        
        // Helper function to add CSRF token to fetch requests
        function fetchWithCSRF(url, options = {}) {
            options.headers = options.headers || {};
            if (options.method && options.method.toUpperCase() !== 'GET') {
                options.headers['X-CSRFToken'] = getCSRFToken();
            }
            return fetch(url, options);
        }
    </script>

    <!-- Other Custom Scripts -->
    <script src="{{ url_for('static', filename='js/socket-events.js') }}"></script>
    <script src="{{ url_for('static', filename='js/mobile-menu.js') }}"></script>

    {% block head %}{% endblock %}
</head>
<body class="min-h-screen bg-base-100">
    <div class="drawer">
        <input id="main-drawer" type="checkbox" class="drawer-toggle" />
        <div class="drawer-content flex flex-col min-h-screen">
            <!-- Fixed Navbar -->
            <div class="sticky top-0 z-30 flex h-16 w-full justify-center bg-base-100 bg-opacity-90 backdrop-blur transition-all duration-100">
                <nav class="navbar w-full">
                    {% include 'navbar.html' %}
                </nav>
            </div>

            <!-- Toast Container for Notifications -->
            <div class="toast toast-top toast-end z-50">
                <!-- Notifications will be dynamically added here -->
            </div>

            <!-- Flash Messages -->
            <div class="container mx-auto px-4 mt-4">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert {{ 'alert-success' if category == 'success' else 'alert-error' }} shadow-lg mb-4">
                                <div class="flex items-center">
                                    {% if category == 'success' %}
                                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    {% else %}
                                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    {% endif %}
                                    <span>{{ message }}</span>
                                    <button onclick="this.parentElement.parentElement.remove()" class="btn btn-ghost btn-sm btn-circle ml-auto">✕</button>
                                </div>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>

            <!-- Main Content -->
            <main class="flex-grow bg-base-100 mt-6">
                <div class="container mx-auto px-4">
                    {% block content %}{% endblock %}
                </div>
            </main>

            <!-- Footer -->
            <footer class="mt-auto bg-base-200">
                {% include 'footer.html' %}
            </footer>
        </div>

        <!-- Drawer Side (Mobile Menu) -->
        <div class="drawer-side">
            <label for="main-drawer" aria-label="close sidebar" class="drawer-overlay"></label>
            <div class="menu p-4 w-80 min-h-full bg-base-200 text-base-content">
                <!-- Sidebar content here -->
                <div class="flex items-center gap-2 mb-8">
                    <img src="{{ url_for('static', filename='favicon/logo.png') }}" alt="AlgoFactory" class="h-8 w-8">
                    <span class="text-xl font-semibold">AlgoFactory</span>
                </div>
                <ul class="menu menu-lg gap-2">
                    <li>
                        <a href="{{ url_for('dashboard_bp.dashboard') }}" 
                           class="{{ 'active' if request.endpoint == 'dashboard_bp.dashboard' }}">
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('orders_bp.orderbook') }}" 
                           class="{{ 'active' if request.endpoint == 'orders_bp.orderbook' }}">
                            Orderbook
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('orders_bp.tradebook') }}" 
                           class="{{ 'active' if request.endpoint == 'orders_bp.tradebook' }}">
                            Tradebook
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('orders_bp.positions') }}" 
                           class="{{ 'active' if request.endpoint == 'orders_bp.positions' }}">
                            Positions
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('orders_bp.holdings') }}" 
                           class="{{ 'active' if request.endpoint == 'orders_bp.holdings' }}">
                            Holdings
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('tv_json_bp.tradingview_json') }}" 
                           class="{{ 'active' if request.endpoint == 'tv_json_bp.tradingview_json' }}">
                            Tradingview
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('chartink_bp.index') }}" 
                           class="{{ 'active' if request.endpoint.startswith('chartink_bp.') }}">
                            Chartink
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('strategy_bp.index') }}" 
                           class="{{ 'active' if request.endpoint.startswith('strategy_bp.') }}">
                            Strategy
                        </a>
                    </li>
                    <div class="divider"></div>
                    <li>
                        <a href="{{ url_for('auth.change_password') }}" 
                           class="{{ 'active' if request.endpoint == 'auth.change_password' }}">
                            Profile
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('api_key_bp.manage_api_key') }}" 
                           class="{{ 'active' if request.endpoint == 'api_key_bp.manage_api_key' }}">
                            API Key
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('log_bp.view_logs') }}" 
                           class="{{ 'active' if request.endpoint == 'log_bp.view_logs' }}">
                            Logs
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('traffic_bp.traffic_dashboard') }}" 
                           class="{{ 'active' if request.endpoint == 'traffic_bp.traffic_dashboard' }}">
                            Traffic Monitor
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('search_bp.token') }}" 
                           class="{{ 'active' if request.endpoint == 'search_bp.token' }}">
                            Search
                        </a>
                    </li>
                    <li>
                        <a href="https://docs.algofactory.in" target="_blank">
                            Docs
                        </a>
                    </li>
                    <div class="divider"></div>
                    <li>
                        <form method="POST" action="{{ url_for('auth.logout') }}" class="m-0">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" class="btn btn-ghost btn-sm text-error justify-start w-full">
                                Logout
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Master Contract Status Script for Navbar -->
    <script>
        // Navbar Master Contract Status Controller
        class NavbarMasterContractStatus {
            constructor() {
                this.led = document.getElementById('navbar-master-contract-led');
                this.text = document.getElementById('navbar-master-contract-text');
                this.checkInterval = null;
                
                if (this.led) {
                    this.init();
                }
            }

            init() {
                // Check status immediately
                this.checkStatus();
                
                // Set up periodic checking every 10 seconds (less frequent than dashboard)
                this.checkInterval = setInterval(() => this.checkStatus(), 10000);
                
                // Listen for WebSocket events
                if (typeof socket !== 'undefined') {
                    socket.on('master_contract_download', (data) => {
                        this.checkStatus();
                    });
                }
            }

            async checkStatus() {
                try {
                    const response = await fetch('/api/master-contract/status');
                    const data = await response.json();
                    this.updateDisplay(data);
                } catch (error) {
                    console.error('Error checking master contract status:', error);
                }
            }

            updateDisplay(data) {
                if (!this.led) return;
                
                const { status, total_symbols } = data;
                
                switch (status) {
                    case 'success':
                        this.led.className = 'w-2 h-2 rounded-full bg-green-500';
                        if (this.text) {
                            this.text.textContent = 'Ready';
                            this.text.className = 'text-xs hidden sm:inline text-green-600';
                        }
                        // Clear interval once successful
                        if (this.checkInterval) {
                            clearInterval(this.checkInterval);
                            this.checkInterval = null;
                        }
                        break;
                        
                    case 'downloading':
                        this.led.className = 'w-2 h-2 rounded-full bg-yellow-500 animate-pulse';
                        if (this.text) {
                            this.text.textContent = 'Downloading';
                            this.text.className = 'text-xs hidden sm:inline text-yellow-600';
                        }
                        break;
                        
                    case 'error':
                        this.led.className = 'w-2 h-2 rounded-full bg-red-500';
                        if (this.text) {
                            this.text.textContent = 'Error';
                            this.text.className = 'text-xs hidden sm:inline text-red-600';
                        }
                        break;
                        
                    case 'pending':
                        this.led.className = 'w-2 h-2 rounded-full bg-gray-400 animate-pulse';
                        if (this.text) {
                            this.text.textContent = 'Pending';
                            this.text.className = 'text-xs hidden sm:inline text-gray-600';
                        }
                        break;
                        
                    default:
                        this.led.className = 'w-2 h-2 rounded-full bg-gray-400';
                        if (this.text) {
                            this.text.textContent = 'Unknown';
                            this.text.className = 'text-xs hidden sm:inline text-gray-600';
                        }
                }
            }

            destroy() {
                if (this.checkInterval) {
                    clearInterval(this.checkInterval);
                }
            }
        }

        // Initialize navbar status indicator
        document.addEventListener('DOMContentLoaded', () => {
            window.navbarMasterContractStatus = new NavbarMasterContractStatus();
        });
    </script>

    <!-- Toast notification function -->
    <script>
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} shadow-lg mb-4`;
            
            const content = document.createElement('div');
            content.className = 'flex items-center';
            
            // Icon based on type
            let icon = '';
            switch(type) {
                case 'success':
                    icon = '<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
                    break;
                case 'error':
                    icon = '<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
                    break;
                default:
                    icon = '<svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>';
            }
            
            content.innerHTML = `
                ${icon}
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="btn btn-ghost btn-sm btn-circle ml-auto">✕</button>
            `;
            
            toast.appendChild(content);
            document.querySelector('.toast').appendChild(toast);

            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.classList.add('animate-out');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
