{% extends "base.html" %}

{% block head %}
<style>
    .search-results {
        @apply absolute w-full max-h-[300px] overflow-y-auto bg-base-100 border border-base-200 rounded-lg shadow-lg z-50;
        display: none;
        top: 100%;
        margin-top: 4px;
    }

    .result-item {
        @apply p-4 border-b border-base-200 hover:bg-base-200 cursor-pointer transition-all duration-200;
    }

    .result-item:last-child {
        @apply border-b-0;
    }

    .loading-indicator {
        @apply absolute right-3 top-1/2 -translate-y-1/2;
        display: none;
    }

    .search-tips {
        @apply mt-8 p-4 bg-base-200 rounded-lg;
    }

    .search-tips ul {
        @apply mt-2 space-y-2;
    }

    .search-tips li {
        @apply flex items-center gap-2;
    }

    .search-tips li::before {
        content: "•";
        @apply text-primary;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-3xl mx-auto">
    <!-- Header -->
    <div class="section-header text-center">
        <h1 class="text-3xl font-bold">Symbol Search</h1>
        <p class="text-base-content/60 mt-2">Search for symbols across different exchanges to get detailed information</p>
    </div>

    <!-- Search Form -->
    <div class="card bg-base-100 shadow-lg">
        <div class="card-body">
            <form action="{{ url_for('search_bp.search') }}" method="get" id="searchForm" class="space-y-6">
                <!-- Symbol Search -->
                <div class="form-control w-full">
                    <label class="label">
                        <span class="label-text">Symbol, Name, or Token</span>
                    </label>
                    <div class="relative">
                        <input type="text" 
                               name="symbol" 
                               id="symbolSearch" 
                               required 
                               class="input input-bordered w-full pr-10" 
                               placeholder="Enter your search query..."
                               autocomplete="off">
                        <div class="loading-indicator">
                            <span class="loading loading-spinner loading-sm"></span>
                        </div>
                        <div id="searchResults" class="search-results"></div>
                    </div>
                </div>

                <!-- Exchange Select -->
                <div class="form-control w-full">
                    <label class="label">
                        <span class="label-text">Exchange</span>
                    </label>
                    <select name="exchange" id="exchangeSelect" required class="select select-bordered w-full">
                        <option value="">Select Exchange</option>
                        <option value="NSE">NSE - National Stock Exchange</option>
                        <option value="NFO">NFO - NSE Futures & Options</option>
                        <option value="BSE">BSE - Bombay Stock Exchange</option>
                        <option value="BFO">BFO - BSE Futures & Options</option>
                        <option value="CDS">CDS - Currency Derivatives</option>
                        <option value="MCX">MCX - Multi Commodity Exchange</option>
                        <option value="NSE_INDEX">NSE_INDEX - National Stock Exchange Index</option>
                        <option value="BSE_INDEX">BSE_INDEX - Bombay Stock Exchange Index</option>
                    </select>
                </div>

                <!-- Search Button -->
                <button type="submit" class="btn btn-primary w-full gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    Search Symbol
                </button>
            </form>
        </div>
    </div>

    <!-- Search Tips -->
    <div class="search-tips">
        <div class="flex items-start gap-3">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary shrink-0 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
                <h3 class="font-bold text-lg">Search Tips</h3>
                <ul class="text-base-content/70">
                    <li>Search by symbol name (e.g., RELIANCE, INFY)</li>
                    <li>Search by company name (e.g., Reliance Industries)</li>
                    <li>Search by token number</li>
                    <li>Select appropriate exchange for accurate results</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
let debounceTimeout;
const loadingIndicator = document.querySelector('.loading-indicator');
const symbolInput = document.getElementById('symbolSearch');
const searchResults = document.getElementById('searchResults');
const exchangeSelect = document.getElementById('exchangeSelect');

symbolInput.addEventListener('input', function(e) {
    clearTimeout(debounceTimeout);
    const query = e.target.value.trim();
    const exchange = exchangeSelect.value;
    
    if (query.length < 2) {
        searchResults.style.display = 'none';
        return;
    }
    
    debounceTimeout = setTimeout(() => {
        fetchSearchResults(query, exchange);
    }, 300);
});

exchangeSelect.addEventListener('change', function(e) {
    const query = symbolInput.value.trim();
    if (query.length >= 2) {
        fetchSearchResults(query, e.target.value);
    }
});

document.addEventListener('click', function(e) {
    if (!symbolInput.contains(e.target) && !searchResults.contains(e.target)) {
        searchResults.style.display = 'none';
    }
});

async function fetchSearchResults(query, exchange) {
    try {
        loadingIndicator.style.display = 'block';
        const response = await fetch(`{{ url_for('search_bp.api_search') }}?q=${encodeURIComponent(query)}&exchange=${encodeURIComponent(exchange)}`);
        const data = await response.json();
        
        searchResults.innerHTML = '';
        
        if (data.results.length > 0) {
            data.results.forEach(result => {
                const div = document.createElement('div');
                div.className = 'result-item';
                div.innerHTML = `
                    <div class="flex items-center justify-between">
                        <span class="font-medium">${result.symbol}</span>
                        <span class="badge badge-${result.exchange.toLowerCase()}">${result.exchange}</span>
                    </div>
                    <div class="text-sm text-base-content/70 mt-1">${result.name || ''}</div>
                    <div class="text-xs text-base-content/60 mt-1">Token: ${result.token}</div>
                `;
                div.addEventListener('click', () => {
                    symbolInput.value = result.symbol;
                    exchangeSelect.value = result.exchange;
                    searchResults.style.display = 'none';
                });
                searchResults.appendChild(div);
            });
            searchResults.style.display = 'block';
        } else {
            searchResults.style.display = 'none';
        }
    } catch (error) {
        console.error('Error:', error);
        showToast('Error fetching search results', 'error');
    } finally {
        loadingIndicator.style.display = 'none';
    }
}
</script>
{% endblock %}
