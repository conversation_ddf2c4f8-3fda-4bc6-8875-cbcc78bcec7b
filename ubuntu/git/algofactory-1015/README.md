# OpenAlgo - Take Control of Your Algo Platform

<div align="center">

[![PyPI Downloads](https://static.pepy.tech/badge/openalgo)](https://pepy.tech/projects/openalgo)
[![PyPI Downloads](https://static.pepy.tech/badge/openalgo/month)](https://pepy.tech/projects/openalgo)
[![X (formerly Twitter) Follow](https://img.shields.io/twitter/follow/openalgoHQ)](https://twitter.com/openalgoHQ)
[![YouTube Channel Subscribers](https://img.shields.io/youtube/channel/subscribers/UCw7eVneIEyiTApy4RtxrJsQ)](https://youtube.com/@openalgoHQ)
[![Discord](https://img.shields.io/discord/1219847221055455263)](https://discord.com/invite/UPh7QPsNhP)

</div>

OpenAlgo is an open-source, Flask-based Python application designed to bridge the gap between traders and major trading platforms such as Amibroker, Tradingview, Python, Chartink, MetaTrader, Excel, and Google Spreadsheets. With a focus on simplifying algotrading, OpenAlgo facilitates easy integration, automation, and execution of trading strategies, providing a user-friendly interface to enhance trading performance.

## Installation Guide
[![OpenAlgo Windows Installation Tutorial](https://img.youtube.com/vi/PCPAeDKTh50/0.jpg)](https://www.youtube.com/watch?v=PCPAeDKTh50 "Watch the Installation Tutorial")

For detailed installation instructions, please refer to [INSTALL.md](INSTALL.md)

## What is OpenAlgo?
[![What is OpenAlgo](https://img.youtube.com/vi/kAS3jTb3OkI/0.jpg)](https://www.youtube.com/watch?v=kAS3jTb3OkI "Watch the OpenAlgo Tutorial Video")

## Supported Brokers

- **5paisa**
- **5paisa (XTS)**
- **AliceBlue**
- **AngelOne**
- **Dhan**
- **Dhan (Sandbox)**
- **Firstock**
- **Flattrade**
- **Fyers**
- **ICICI Direct**
- **Jainam (Retail)**
- **Jainam (Dealer)**
- **Kotak** 
- **Paytm**
- **Pocketful**
- **Shoonya**
- **Tradejini**
- **Upstox**
- **Wisdom Capital**
- **Zebu**
- **Zerodha**

## Features

- **ChartInk Platform Integration**: 
  - Direct integration with ChartInk for strategy execution
  - Automated scanning and trading based on ChartInk signals
  - Real-time strategy monitoring and management
  - Custom strategy configuration and deployment
  - Seamless execution of ChartInk strategies through your broker

- **Advanced Monitoring Tools**:
  - **Latency Monitor**: Track and analyze order execution performance
    - Real-time latency tracking across different brokers
    - Detailed breakdown of execution times
    - Performance comparison between brokers
    - Order execution success rates and patterns
  - **Traffic Monitor**: Monitor system performance and API usage
    - Real-time API request tracking
    - Endpoint-specific analytics
    - Error rate monitoring
    - System performance metrics
  For detailed information about monitoring tools, see [traffic.md](docs/traffic.md)

- **Modern UI with DaisyUI**: 
  - Sleek and responsive interface built with DaisyUI components
  - Three distinct themes:
    - Light theme for normal mode
    - Dark theme for reduced eye strain
    - Garden theme for analyzer mode
  - Instant theme switching with state preservation
  - Theme-aware syntax highlighting for code and JSON
  - Mobile-friendly layout with drawer navigation

- **Real-Time Trading Updates**:
  - Instant order book updates via WebSocket
  - Live trade book monitoring with automatic refresh
  - Real-time position tracking
  - Dynamic log updates for trade activities
  - Contextual notifications with sound alerts

- **API Analyzer**:
  - Real-time request validation and testing
  - Strategy testing without live execution
  - Detailed request/response analysis
  - Comprehensive error detection
  - Dedicated garden theme for better focus
  - See [Analyzer.md](docs/Analyzer.md) for detailed documentation

- **Comprehensive Integration**: Seamlessly connect with Amibroker, Tradingview, Excel, and Google Spreadsheets for smooth data and strategy transition.

- **User-Friendly Interface**: A straightforward Flask-based application interface accessible to traders of all levels of expertise.

- **Real-Time Execution**: Implement your trading strategies in real time, ensuring immediate action to capitalize on market opportunities.

- **Customizable Strategies**: Easily adapt and tailor your trading strategies to meet your specific needs, with extensive options for customization and automation.

- **Secure and Reliable**: With a focus on security and reliability, OpenAlgo provides a dependable platform for your algotrading activities, safeguarding your data and trades.

## Documentation

For detailed documentation on OpenAlgo, including setup guides, API references, and usage examples, refer to [https://docs.openalgo.in](https://docs.openalgo.in)

### Minimum Hardware Requirements

To run OpenAlgo we recommend:
- 2GB RAM
- 1GB disk space
- 2vCPU

## Contributing

We welcome contributions to OpenAlgo! If you're interested in improving the application or adding new features, please feel free to fork the repository, make your changes, and submit a pull request.

## License

OpenAlgo is released under the AGPL V3.0 License. See the `LICENSE` file for more details.

## Star History

[![Star History Chart](https://api.star-history.com/svg?repos=marketcalls/openalgo&type=Date)](https://star-history.com/#marketcalls/openalgo&Date)

## Disclaimer

This software is for educational purposes only. Do not risk money which
you are afraid to lose. USE THE SOFTWARE AT YOUR OWN RISK. THE AUTHORS
AND ALL AFFILIATES ASSUME NO RESPONSIBILITY FOR YOUR TRADING RESULTS.

## Support

For any questions not covered by the documentation or for further information about OpenAlgo, join our [Discord server](https://discord.com/invite/UPh7QPsNhP).
