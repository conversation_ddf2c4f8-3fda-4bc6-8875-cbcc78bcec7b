/* Import Tailwind CSS base styles */
@tailwind base;

/* Import Tailwind CSS component styles */
@tailwind components;

/* Import Tailwind CSS utility styles */
@tailwind utilities;

/* Custom base styles */
@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        @apply min-h-screen flex flex-col;
    }
}

/* Custom components */
@layer components {
    /* Layout components */
    main {
        @apply flex-1 w-full;
    }

    .container {
        @apply w-full max-w-[1280px] mx-auto px-4;
    }

    /* Content spacing */
    .content-section {
        @apply py-16 space-y-8;
    }

    .section-header {
        @apply mb-12;
    }

    /* Theme consistency */
    [data-theme="light"] {
        @apply bg-base-100 text-base-content;
    }

    [data-theme="dark"] {
        @apply bg-base-100 text-base-content;
    }

    /* Theme switcher */
    .theme-switcher {
        @apply opacity-100 pointer-events-auto transition-opacity duration-300;
    }

    .theme-switcher.disabled {
        @apply opacity-50 pointer-events-none;
    }

    /* Mode badge styles */
    #mode-badge {
        @apply inline-flex items-center px-4 py-2 rounded-lg font-medium text-sm leading-none whitespace-nowrap mr-2;
    }

    #mode-badge.badge-success {
        @apply bg-success text-white;
    }

    #mode-badge.badge-warning {
        @apply bg-warning text-white;
    }

    /* Mode controller container */
    .mode-controller-container {
        @apply flex items-center gap-2 mr-2;
    }

    /* Card styles */
    .card {
        @apply bg-base-100 transition-all duration-200;
    }

    .card:hover {
        @apply -translate-y-0.5;
    }

    /* Stats cards */
    .stat-card {
        @apply bg-base-100 transition-all duration-300;
    }

    .stat-card:hover {
        @apply -translate-y-0.5;
    }

    /* Form styles */
    .form-control {
        @apply mb-4;
    }

    .input:focus, 
    .select:focus {
        @apply outline-none border-primary ring-2 ring-primary/20;
    }

    /* Table styles */
    .table-container {
        @apply bg-base-100 overflow-x-auto my-4 rounded-lg;
    }

    .table {
        @apply w-full border-separate border-spacing-0;
    }

    .table th {
        @apply sticky top-0 bg-base-200 z-10;
    }

    .drawer-side .table th {
        @apply bg-base-300 border-b border-base-200;
    }

    /* Toast notifications */
    #toast-container {
        @apply fixed top-4 right-4 z-50 max-w-sm pointer-events-none;
    }

    #toast-container > * {
        @apply pointer-events-auto;
    }

    .toast {
        @apply animate-[slideIn_0.3s_ease-out];
    }

    .toast.fade-out {
        @apply animate-[slideOut_0.3s_ease-out_forwards];
    }

    /* Loading spinner */
    .loading {
        @apply inline-block w-6 h-6 border-2 border-base-200 rounded-full border-t-primary animate-spin;
    }

    /* Navigation */
    .navbar {
        @apply bg-base-100 border-b border-base-200;
    }

    /* Footer */
    footer {
        @apply w-full border-t border-base-200;
    }

    /* Button styles */
    .btn {
        @apply normal-case active:scale-[0.98];
    }

    /* Code block styles */
    pre[class*="language-"] {
        @apply bg-base-200 m-0 rounded-lg;
        background-color: hsl(var(--b2)) !important;
    }

    code[class*="language-"] {
        @apply text-base-content no-underline;
        color: hsl(var(--bc)) !important;
    }

    /* Search results */
    .search-results {
        @apply absolute w-full max-h-[300px] overflow-y-auto bg-base-100 border border-base-200 rounded-lg shadow-lg z-50 hidden;
    }

    .result-item {
        @apply p-4 border-b border-base-200 hover:bg-base-200 cursor-pointer transition-all duration-200;
    }

    .result-item:last-child {
        @apply border-b-0;
    }
}

/* Exchange badge colors */
.badge-nse {
    background-color: #4CAF50 !important;
    color: white !important;
}

.badge-bse {
    background-color: #2196F3 !important;
    color: white !important;
}

.badge-nfo {
    background-color: #9C27B0 !important;
    color: white !important;
}

.badge-mcx {
    background-color: #FF9800 !important;
    color: white !important;
}

.badge-cds {
    background-color: #E91E63 !important;
    color: white !important;
}

.badge-bfo {
    background-color: #673AB7 !important;
    color: white !important;
}

/* Responsive adjustments */
@layer utilities {
    @media (max-width: 640px) {
        .container {
            @apply px-2;
        }

        #toast-container {
            @apply left-4 right-4 max-w-none;
        }

        .card-body {
            @apply p-4;
        }

        #mode-badge {
            @apply text-xs py-1.5 px-2 min-h-6 leading-none;
        }

        .navbar-end {
            @apply gap-2;
        }

        .mode-controller-container {
            @apply gap-1;
        }

        .navbar-end .btn-circle {
            @apply w-10 h-10 min-h-10;
        }

        .navbar-end .btn-circle svg {
            @apply w-5 h-5;
        }
    }

    @media (min-width: 641px) and (max-width: 1024px) {
        #mode-badge {
            @apply text-[0.8125rem] py-1.5 px-2;
        }

        .navbar-end {
            @apply gap-3;
        }

        .navbar-end .btn-circle {
            @apply w-11 h-11 min-h-11;
        }
    }
}

/* Animation keyframes */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}
