<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .background { fill: #FFFFFF; }
      .text { fill: #000000; font-family: Arial, sans-serif; font-weight: bold; font-size: 200px; }
      .border { fill: none; stroke: #E5E7EB; stroke-width: 8; }
    </style>
  </defs>

  <!-- White background square -->
  <rect x="0" y="0" width="512" height="512" class="background"/>

  <!-- Border -->
  <rect x="0" y="0" width="512" height="512" class="border"/>

  <!-- AF Text -->
  <text x="256" y="320" text-anchor="middle" class="text">AF</text>
</svg>