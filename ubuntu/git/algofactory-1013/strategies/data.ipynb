{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>close</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>open</th>\n", "      <th>volume</th>\n", "    </tr>\n", "    <tr>\n", "      <th>timestamp</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2025-01-01 09:15:00+05:30</th>\n", "      <td>795.25</td>\n", "      <td>798.00</td>\n", "      <td>792.50</td>\n", "      <td>795.55</td>\n", "      <td>190321</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 09:20:00+05:30</th>\n", "      <td>794.75</td>\n", "      <td>795.60</td>\n", "      <td>793.15</td>\n", "      <td>795.10</td>\n", "      <td>109048</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 09:25:00+05:30</th>\n", "      <td>795.45</td>\n", "      <td>796.00</td>\n", "      <td>794.20</td>\n", "      <td>794.60</td>\n", "      <td>55949</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 09:30:00+05:30</th>\n", "      <td>793.40</td>\n", "      <td>796.00</td>\n", "      <td>793.00</td>\n", "      <td>795.45</td>\n", "      <td>85310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-01 09:35:00+05:30</th>\n", "      <td>793.80</td>\n", "      <td>793.95</td>\n", "      <td>793.15</td>\n", "      <td>793.50</td>\n", "      <td>36133</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-23 15:15:00+05:30</th>\n", "      <td>745.25</td>\n", "      <td>746.45</td>\n", "      <td>745.15</td>\n", "      <td>745.90</td>\n", "      <td>551123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-23 15:20:00+05:30</th>\n", "      <td>746.05</td>\n", "      <td>746.35</td>\n", "      <td>745.25</td>\n", "      <td>745.40</td>\n", "      <td>341460</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-23 15:25:00+05:30</th>\n", "      <td>746.25</td>\n", "      <td>746.65</td>\n", "      <td>745.25</td>\n", "      <td>745.95</td>\n", "      <td>229830</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-24 09:15:00+05:30</th>\n", "      <td>749.70</td>\n", "      <td>750.55</td>\n", "      <td>746.00</td>\n", "      <td>749.90</td>\n", "      <td>358900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-01-24 09:20:00+05:30</th>\n", "      <td>746.50</td>\n", "      <td>749.70</td>\n", "      <td>746.15</td>\n", "      <td>749.65</td>\n", "      <td>395513</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1277 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                            close    high     low    open  volume\n", "timestamp                                                        \n", "2025-01-01 09:15:00+05:30  795.25  798.00  792.50  795.55  190321\n", "2025-01-01 09:20:00+05:30  794.75  795.60  793.15  795.10  109048\n", "2025-01-01 09:25:00+05:30  795.45  796.00  794.20  794.60   55949\n", "2025-01-01 09:30:00+05:30  793.40  796.00  793.00  795.45   85310\n", "2025-01-01 09:35:00+05:30  793.80  793.95  793.15  793.50   36133\n", "...                           ...     ...     ...     ...     ...\n", "2025-01-23 15:15:00+05:30  745.25  746.45  745.15  745.90  551123\n", "2025-01-23 15:20:00+05:30  746.05  746.35  745.25  745.40  341460\n", "2025-01-23 15:25:00+05:30  746.25  746.65  745.25  745.95  229830\n", "2025-01-24 09:15:00+05:30  749.70  750.55  746.00  749.90  358900\n", "2025-01-24 09:20:00+05:30  746.50  749.70  746.15  749.65  395513\n", "\n", "[1277 rows x 5 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["from openalgo import api\n", "\n", "# Initialize the API client\n", "client = api(api_key='openalgo-api-key', host='http://127.0.0.1:5000')\n", "\n", "# Fetch historical data for SBIN\n", "df = client.history(\n", "    symbol=\"SBIN\",\n", "    exchange=\"NSE\",\n", "    interval=\"5m\",\n", "    start_date=\"2025-01-01\",\n", "    end_date=\"2025-01-24\"\n", ")\n", "\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}