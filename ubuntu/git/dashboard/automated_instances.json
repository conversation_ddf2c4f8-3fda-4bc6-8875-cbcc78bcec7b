{"instances": {"1010": {"name": "algofactory-1010", "port": 1010, "created": "2025-06-22T21:42:43.388824", "auto_start": true, "nginx_enabled": false, "ssl_enabled": false, "service_name": "algofactory-1010"}, "1011": {"name": "algofactory-1011", "port": 1011, "created": "2025-06-22T21:42:43.388859", "auto_start": true, "nginx_enabled": false, "ssl_enabled": false, "service_name": "algofactory-1011"}, "1012": {"name": "algofactory-1012", "port": 1012, "created": "2025-06-22T21:42:43.388872", "auto_start": true, "nginx_enabled": false, "ssl_enabled": false, "service_name": "algofactory-1012"}, "1013": {"name": "algofactory-1013", "port": 1013, "created": "2025-06-22T22:09:36.641204", "auto_start": true, "nginx_enabled": true, "ssl_enabled": true, "service_name": "algofactory-1013"}}, "settings": {"auto_ssl": true, "auto_nginx": true}}