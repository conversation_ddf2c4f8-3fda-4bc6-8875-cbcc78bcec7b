echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
cd ~/git
bash
source ~/shared-venv/bin/activate
python run_dashboard.py
cd /home/<USER>/git && python3 run_dashboard.py
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git && python3 run_dashboard.py
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -m streamlit run simple_dashboard.py --server.port 8502 --server.address 0.0.0.0 --server.headless true
sudo apt
source /home/<USER>/shared-venv/bin/activate && python3 -m streamlit run simple_dashboard.py --server.port 8502 --server.address 0.0.0.0 --server.headless true
sudo netstat -tlnp | grep :8502
ss -tlnp | grep :8502
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "import streamlit; print('Streamlit OK'); import simple_dashboard; print('Dashboard import OK')"
ps aux | grep streamlit
ss -tlnp | grep :8502
curl -I http://localhost:8502
sudo systemctl status nginx
curl -I http://dashboard.algofactory.in
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "from auth_config import auth_config; print('Auth enabled:', auth_config.is_auth_enabled())"
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
from auth_manager import AuthManager
auth = AuthManager()
success, message = auth.add_user('admin', 'AlgoFactory123!', 'admin', '<EMAIL>')
print(f'User creation: {success}')
print(f'Message: {message}')
if success:
    print('✅ Admin user created successfully!')
    print('Username: admin')
    print('Password: AlgoFactory123!')
"
sleep 5 && curl -I http://localhost:8502
ps aux | grep streamlit
ss -tlnp | grep :8502
curl -I http://localhost:8502
sudo nginx -t
ls -la /etc/nginx/sites-enabled/
sudo tail -10 /var/log/nginx/error.log
sudo rm /etc/nginx/sites-enabled/1010.algofactory.in
sudo nginx -t && sudo systemctl reload nginx
curl -I http://dashboard.algofactory.in
ls -la /etc/nginx/sites-enabled/
sudo cat /etc/nginx/sites-available/dashboard.algofactory.in.conf
curl -v http://127.0.0.1:8502
sudo tail -5 /var/log/nginx/dashboard.algofactory.in.error.log
sudo cp dashboard.algofactory.in.conf /etc/nginx/sites-available/ && sudo nginx -t && sudo systemctl reload nginx
cd /home/<USER>/git/nginx-config && sudo cp dashboard.algofactory.in.conf /etc/nginx/sites-available/ && sudo nginx -t && sudo systemctl reload nginx
curl -I http://dashboard.algofactory.in
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
from auth_manager import AuthManager
auth = AuthManager()

# Update the admin password
hashed_password = auth.hash_password('Ap9211###')
auth.config['users']['admin']['password_hash'] = hashed_password
auth.save_config()
print('✅ Admin password updated successfully!')
print('Username: admin')
print('Password: Ap9211###')
"
sleep 5 && curl -I http://localhost:8502
sudo systemctl stop nginx
sudo apt update && sudo apt install -y certbot
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
source /home/<USER>/shared-venv/bin/activate && python3 -c "import pandas_ta; print('✅ pandas_ta:', pandas_ta.__version__)"
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/algofactory && python3 -c "import flask; print('Flask OK')"
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
python3 -m http.server 8080
sudo python3 -m http.server 80
cd..
cd ~/algofactory
ubuntu
bash
echo 'Terminal capability test'
echo 'Terminal capability test'
free -h
sudo rm -f /etc/nginx/sites-enabled/backup.algofactory.in.conf /etc/nginx/sites-available/backup.algofactory.in.conf && sudo nginx -t && sudo systemctl reload nginx
sudo cp /tmp/dashboard.service /etc/systemd/system/algofactory-dashboard.service
sudo systemctl daemon-reload
sudo systemctl enable algofactory-dashboard.service
sudo systemctl start algofactory-dashboard.service
curl -I https://dashboard.algofactory.in
ss -tlnp | grep :5000
curl -I http://localhost:5000
sudo nginx -t && sudo systemctl reload nginx
curl -I http://5000.algofactory.in
cat > /tmp/algofactory.service << 'EOF'
[Unit]
Description=AlgoFactory - Flask Trading Application
After=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/git/algofactory
Environment=PATH=/home/<USER>/shared-venv/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/home/<USER>/shared-venv/bin/python3 app.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

cat > /tmp/dashboard.service << 'EOF'
[Unit]
Description=AlgoFactory Dashboard - Streamlit Application
After=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/git/dashboard
Environment=PATH=/home/<USER>/shared-venv/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/home/<USER>/shared-venv/bin/python3 -m streamlit run simple_dashboard.py --server.port 8502 --server.address 0.0.0.0 --server.headless true
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

ps aux --sort=-%mem | head -15
ps aux | grep -E "(streamlit|python.*app\.py|flask|algofactory)" | grep -v grep
ss -tlnp | grep -E ":(5000|8502|8080|443|80)"
rm -f /home/<USER>/algofactory-backup-*.tar.gz /home/<USER>/backup-part-* && rm -rf /tmp/backup
pkill -f "streamlit\|flask\|app\.py" 2>/dev/null || echo "No streamlit/flask processes found"
sudo lsof -i :5000 -i :8502 2>/dev/null || echo "No processes on ports 5000 or 8502"
ss -tlnp
free -h && echo "=== Top Memory Consumers ===" && ps aux --sort=-%mem | head -8
sudo fallocate -l 1G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
free -h
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
sudo sysctl vm.swappiness=10
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
sudo cp /tmp/algofactory-dashboard.service /etc/systemd/system/
sudo cp /home/<USER>/algofactory-dashboard.service /etc/systemd/system/
sudo tee /etc/systemd/system/algofactory-dashboard.service > /dev/null << 'EOF'
[Unit]
Description=AlgoFactory Dashboard - Streamlit Application
Documentation=https://streamlit.io/
After=network.target
Wants=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/git/dashboard
Environment=PATH=/home/<USER>/shared-venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ExecStart=/home/<USER>/shared-venv/bin/python3 -m streamlit run simple_dashboard.py --server.port 8502 --server.address 0.0.0.0 --server.headless true
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=algofactory-dashboard

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=false
ReadWritePaths=/home/<USER>/git/dashboard
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# Resource limits
LimitNOFILE=65536
MemoryMax=300M

[Install]
WantedBy=multi-user.target
EOF

curl -I -H "Host: 5000.algofactory.in" http://**************
ls -la /etc/nginx/sites-enabled/
cd /home/<USER>/git/nginx-config && sudo cp 5000.algofactory.in-http.conf /etc/nginx/sites-available/ && sudo nginx -t
sudo systemctl reload nginx
curl -I http://**************
curl -s http://************** | head -10
echo 'Terminal capability test'
echo 'Terminal capability test'
sudo nginx -t
sudo systemctl start nginx
curl -I https://5000.algofactory.in
curl -I http://5000.algofactory.in
ss -tlnp | grep -E ":(5000|8502|8765|5555)"
sudo systemctl restart algofactory-dashboard.service
sleep 3 && curl -I https://dashboard.algofactory.in
ps aux | grep "python.*app.py" | grep -v grep
kill 3267
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/algofactory && python3 app.py
sleep 3 && curl -I https://5000.algofactory.in
curl -I -H "Host: 5000.algofactory.in" https://**************
sudo rm /etc/nginx/sites-enabled/1015.algofactory.in.conf
sudo nginx -t && sudo systemctl start nginx
curl -I https://5000.algofactory.in
sudo systemctl status algofactory-dashboard.service
cd /home/<USER>/git/nginx-config && sudo cp 5000.algofactory.in-http.conf /etc/nginx/sites-available/ && sudo ln -sf /etc/nginx/sites-available/5000.algofactory.in-http.conf /etc/nginx/sites-enabled/
sudo systemctl stop nginx
sudo certbot certonly --standalone --email <EMAIL> --agree-tos --no-eff-email -d 5000.algofactory.in
cd /home/<USER>/git/nginx-config && mv 5000.algofactory.in-http.conf 5000.algofactory.in.conf
sudo cp 5000.algofactory.in.conf /etc/nginx/sites-available/ && sudo rm -f /etc/nginx/sites-enabled/5000.algofactory.in-http.conf && sudo ln -sf /etc/nginx/sites-available/5000.algofactory.in.conf /etc/nginx/sites-enabled/
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
from automated_instance_manager import AutomatedInstanceManager
manager = AutomatedInstanceManager()
print('✅ Automated Instance Manager loaded successfully!')
print('Available ports:', manager.get_available_ports())
print('Current instances:', len(manager.get_instances()))
"
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
print('Testing enhanced dashboard imports...')
try:
    from automated_instance_manager import AutomatedInstanceManager
    print('✅ AutomatedInstanceManager imported successfully')
    manager = AutomatedInstanceManager()
    print('✅ Manager initialized successfully')
    print('Available ports:', manager.get_available_ports())
except Exception as e:
    print(f'❌ Error: {e}')
    import traceback
    traceback.print_exc()
"
sudo systemctl status nginx
ss -tlnp | grep -E ":(5000|12000|15000)"
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
from automated_instance_manager import AutomatedInstanceManager
manager = AutomatedInstanceManager()

# Test port calculation for port 1015
port = 1015
port_offset = port % 100
websocket_port = 12000 + port_offset
zmq_port = 15000 + port_offset

print(f'Port calculation test for {port}:')
print(f'  Main Port: {port}')
print(f'  Port Offset: {port_offset}')
print(f'  WebSocket Port: {websocket_port}')
print(f'  ZMQ Port: {zmq_port}')
print()

# Test for port 5000
port = 5000
port_offset = port % 100
websocket_port = 12000 + port_offset
zmq_port = 15000 + port_offset

print(f'Port calculation test for {port}:')
print(f'  Main Port: {port}')
print(f'  Port Offset: {port_offset}')
print(f'  WebSocket Port: {websocket_port}')
print(f'  ZMQ Port: {zmq_port}')
"
source /home/<USER>/shared-venv/bin/activate && which python3 && python3 --version
ls -la /home/<USER>/git/ | grep algofactory
sudo systemctl status algofactory-1015.service
sudo journalctl -u algofactory-1015.service --no-pager -n 20
sudo systemctl restart algofactory-1015.service
sleep 5 && sudo systemctl status algofactory-1015.service --no-pager
sudo journalctl -u algofactory-1015.service --no-pager -n 10
ss -tlnp | grep :1015
cat /proc/sys/net/ipv4/ip_unprivileged_port_start
sudo setcap 'cap_net_bind_service=+ep' /home/<USER>/shared-venv/bin/python3
readlink -f /home/<USER>/shared-venv/bin/python3
sudo setcap 'cap_net_bind_service=+ep' /usr/bin/python3.12
cd /home/<USER>/git/algofactory-1015 && source /home/<USER>/shared-venv/bin/activate && python3 app.py
sleep 3 && curl -I http://127.0.0.1:1015
ls -la /etc/nginx/sites-enabled/
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
from automated_instance_manager import AutomatedInstanceManager
manager = AutomatedInstanceManager()
instances = manager.get_instances()
print('Current instances:')
for instance in instances:
    print(f'  Port {instance[\"Port\"]}: {instance[\"Status\"]}')
"
for port in 1010 1011 1012 1014; do   echo "Checking algofactory-$port:";   if [ -f "/home/<USER>/git/algofactory-$port/.env" ]; then     grep -E "WEBSOCKET_PORT|ZMQ_PORT" "/home/<USER>/git/algofactory-$port/.env";   else     echo "  .env file not found";   fi;   echo; done
for port in 1010 1011 1012 1014; do   echo "Fixing algofactory-$port:";   if [ -f "/home/<USER>/git/algofactory-$port/.env" ]; then
    zmq_port=$((15000 + port % 100));     echo "  Setting ZMQ_PORT to $zmq_port";     sed -i "s/ZMQ_PORT='[0-9]*'/ZMQ_PORT='$zmq_port'/" "/home/<USER>/git/algofactory-$port/.env";   fi; done
for port in 1010 1011 1012 1014 1015; do   echo "algofactory-$port ports:";   if [ -f "/home/<USER>/git/algofactory-$port/.env" ]; then     grep -E "FLASK_PORT|WEBSOCKET_PORT|ZMQ_PORT" "/home/<USER>/git/algofactory-$port/.env";   fi;   echo; done
systemctl is-active algofactory-1015.service
sudo systemctl restart algofactory-1015.service
echo "Checking all AlgoFactory instances:"
echo "=================================="
for port in 1010 1011 1012 1014 1015; do   echo "Port $port:";   
  if ss -tln | grep -q ":$port "; then     echo "  ✅ Port $port is listening";   else     echo "  ❌ Port $port is not listening";   fi;   
  if curl -s -I http://127.0.0.1:$port | head -1 | grep -q "200 OK"; then     echo "  ✅ HTTP response OK";   else     echo "  ❌ HTTP response failed";   fi;   
  status=$(systemctl is-active algofactory-$port.service 2>/dev/null || echo "inactive");   echo "  📊 Service status: $status";   echo; done
ss -tln | grep -E ":(1010|1011|1012|1014|1015) "
echo "Final verification of all instances:"
echo "===================================="
for port in 1010 1011 1012 1014 1015; do   echo "Testing port $port:";   
  if ss -tln | grep -q "0.0.0.0:$port "; then     echo "  ✅ Port $port listening on all interfaces";   else     echo "  ❌ Port $port not accessible externally";   fi;   
  if curl -s -I http://127.0.0.1:$port | head -1 | grep -q "200"; then     echo "  ✅ HTTP response successful";   else     echo "  ❌ HTTP response failed";   fi;      echo; done
ss -tln | grep -E ":(1010|1011|1012|1014|1015) " && echo "---" && curl -s -I http://127.0.0.1:1010 | head -1
sudo systemctl restart algofactory-dashboard.service
sleep 3 && curl -I https://dashboard.algofactory.in
echo "🚀 ALGOFACTORY INSTANCE STATUS SUMMARY"
echo "======================================="
echo
echo "📊 RUNNING INSTANCES:"
for port in 1010 1011 1012 1014 1015; do   status=$(systemctl is-active algofactory-$port.service 2>/dev/null || echo "inactive");   if [ "$status" = "active" ]; then     echo "  ✅ Port $port: RUNNING (algofactory-$port)";   else     echo "  ❌ Port $port: STOPPED";   fi; done
echo
echo "🔌 PORT CONFIGURATION:"
for port in 1010 1011 1012 1014 1015; do   if [ -f "/home/<USER>/git/algofactory-$port/.env" ]; then     websocket_port=$(grep "WEBSOCKET_PORT" "/home/<USER>/git/algofactory-$port/.env" | cut -d"'" -f2);     zmq_port=$(grep "ZMQ_PORT" "/home/<USER>/git/algofactory-$port/.env" | cut -d"'" -f2);     echo "  Port $port: WebSocket=$websocket_port, ZMQ=$zmq_port";   fi; done
echo
echo "🌐 AVAILABLE SERVICES:"
echo "  Dashboard: https://dashboard.algofactory.in"
echo "  Main AlgoFactory: https://5000.algofactory.in"
echo "  Instance 1015: https://1015.algofactory.in (with SSL)"
echo "  Instances 1010-1014: http://127.0.0.1:PORT (manual setup)"
echo
echo "🔒 SECURITY STATUS:"
echo "  ✅ WebSocket ports: localhost only (secure)"
echo "  ✅ ZMQ ports: localhost only (secure)"
echo "  ✅ Flask apps: accessible via nginx proxy"
echo "  ✅ Python binary: granted port binding capabilities"
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
from automated_instance_manager import AutomatedInstanceManager
manager = AutomatedInstanceManager()
instances = manager.get_instances()
print('🚀 All AlgoFactory Instances Status:')
print('====================================')
for instance in instances:
    print(f'Port {instance[\"Port\"]}: {instance[\"Status\"]} - {instance[\"URL\"]}')
    print(f'  Name: {instance[\"Name\"]}')
    print(f'  SSL: {\"✅\" if instance.get(\"SSL\", False) else \"❌\"}')
    print(f'  Nginx: {\"✅\" if instance.get(\"Nginx\", False) else \"❌\"}')
    print()

print(f'Total instances: {len(instances)}')
running_count = len([i for i in instances if \"Running\" in i[\"Status\"]])
print(f'Running instances: {running_count}')
print(f'Available ports: {len(manager.get_available_ports())}')
"
for port in 1010 1011 1012 1014; do   echo "Fixing FLASK_HOST_IP for algofactory-$port...";   sed -i "s/FLASK_HOST_IP='127.0.0.1'/FLASK_HOST_IP='0.0.0.0'/" "/home/<USER>/git/algofactory-$port/.env";   echo "  Updated to 0.0.0.0";   
  echo "  Restarting service...";   sudo systemctl restart algofactory-$port.service;   sleep 2;      status=$(systemctl is-active algofactory-$port.service);   echo "  Status: $status";   echo; done
for port in 1010 1011 1012 1014 1015; do   echo "algofactory-$port FLASK_HOST_IP:";   grep "FLASK_HOST_IP" "/home/<USER>/git/algofactory-$port/.env" 2>/dev/null || echo "  Not found";   echo; done
for port in 1010 1011 1012 1014; do   echo "Starting algofactory-$port...";   sudo systemctl start algofactory-$port.service;   sleep 3;   status=$(systemctl is-active algofactory-$port.service);   echo "  Status: $status";      if [ "$status" = "active" ]; then     echo "  ✅ Successfully started";   else     echo "  ❌ Failed to start - checking logs...";     sudo journalctl -u algofactory-$port.service --no-pager -n 3 | tail -3;   fi;   echo; done
for port in 1010 1011 1012 1014; do   if [ -d "/home/<USER>/git/algofactory-$port" ]; then     echo "Creating systemd service for algofactory-$port...";     
    sudo tee /etc/systemd/system/algofactory-$port.service > /dev/null << EOF
[Unit]
Description=AlgoFactory Instance algofactory-$port - Port $port
After=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/git/algofactory-$port
Environment=PATH=/home/<USER>/shared-venv/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/home/<USER>/shared-venv/bin/python3 app.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
MemoryMax=300M

[Install]
WantedBy=multi-user.target
EOF
     
    sudo systemctl daemon-reload;     sudo systemctl enable algofactory-$port.service;     echo "  Service created and enabled";   else     echo "Directory /home/<USER>/git/algofactory-$port not found";   fi;   echo; done
sudo systemctl restart algofactory-dashboard.service
sleep 3 && curl -I https://dashboard.algofactory.in
sleep 5 && sudo systemctl status algofactory-1015.service --no-pager
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
from automated_instance_manager import AutomatedInstanceManager
manager = AutomatedInstanceManager()
instances = manager.get_instances()
print('✅ Instance Status:')
for instance in instances:
    print(f'  Port {instance[\"Port\"]}: {instance[\"Status\"]} - {instance[\"URL\"]}')
"
for port in 1010 1011 1012 1014; do   echo "Testing instance $port:";   sudo systemctl restart algofactory-$port.service 2>/dev/null || echo "  Service not found";   sleep 2;   status=$(systemctl is-active algofactory-$port.service 2>/dev/null || echo "inactive");   echo "  Status: $status";   echo; done
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
from automated_instance_manager import AutomatedInstanceManager
from datetime import datetime
import json

manager = AutomatedInstanceManager()

# Add existing instances to the configuration
existing_ports = [1010, 1011, 1012, 1014]

for port in existing_ports:
    if str(port) not in manager.config['instances']:
        print(f'Adding port {port} to automated manager...')
        manager.config['instances'][str(port)] = {
            'name': f'algofactory-{port}',
            'port': port,
            'created': datetime.now().isoformat(),
            'auto_start': True,
            'nginx_enabled': False,  # These were created manually
            'ssl_enabled': False,    # These were created manually
            'service_name': f'algofactory-{port}'
        }

# Save the updated configuration
manager.save_config()
print('✅ Updated automated manager configuration')

# Show all instances
instances = manager.get_instances()
print(f'\\nNow tracking {len(instances)} instances:')
for instance in instances:
    print(f'  Port {instance[\"Port\"]}: {instance[\"Status\"]}')
"
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
from automated_instance_manager import AutomatedInstanceManager
manager = AutomatedInstanceManager()

# Test creating a new instance to verify DB folder is empty
available_ports = manager.get_available_ports()
if available_ports:
    test_port = available_ports[0]
    print(f'Testing DB folder cleanup for port {test_port}...')
    
    # Check if algofactory template has db folder
    import os
    template_db = '/home/<USER>/git/algofactory/db'
    if os.path.exists(template_db):
        db_files = os.listdir(template_db)
        print(f'Template DB folder contains: {len(db_files)} files')
        if db_files:
            print(f'Files: {db_files[:3]}...' if len(db_files) > 3 else f'Files: {db_files}')
    else:
        print('Template DB folder does not exist')
else:
    print('No available ports for testing')
"
echo "🖥️  SYSTEM RESOURCE ANALYSIS"
echo "=========================="
echo
echo "💾 MEMORY USAGE:"
free -h
echo
echo "🔥 CPU INFO:"
nproc
cat /proc/cpuinfo | grep "model name" | head -1
echo
echo "💿 DISK USAGE:"
df -h /
echo
echo "🏃 CURRENT PROCESSES:"
ps aux --sort=-%mem | head -10
ps aux --sort=-%mem | head -15
echo "🏭 ALGOFACTORY INSTANCES MEMORY USAGE:"
echo "======================================"
ps aux | grep "python.*app.py" | grep -v grep | while read line; do   pid=$(echo $line | awk '{print $2}');   mem=$(echo $line | awk '{print $4}');   rss=$(echo $line | awk '{print $6}');   port=$(echo $line | awk '{print $11}' | grep -o '[0-9]*' | head -1);   echo "Instance $port: PID=$pid, Memory=${mem}%, RSS=${rss}KB"; done
echo
echo "📊 MEMORY BREAKDOWN:"
echo "VSCode Server: ~356MB (36.3%)"
echo "Dashboard: ~108MB (11.0%)"
echo "System: ~100MB"
echo "Available: ~193MB"
ps aux | grep -E "(algofactory|app\.py)" | grep -v grep
echo "🔍 CHECKING ALGOFACTORY SERVICES:"
echo "================================="
for port in 1010 1011 1012 1014 1015 5000; do   if [ "$port" = "5000" ]; then
    if pgrep -f "python.*app.py" > /dev/null; then       pid=$(pgrep -f "python.*app.py");       mem=$(ps -p $pid -o %mem --no-headers 2>/dev/null | tr -d ' ');       rss=$(ps -p $pid -o rss --no-headers 2>/dev/null | tr -d ' ');       echo "Port 5000 (main): PID=$pid, Memory=${mem}%, RSS=${rss}KB";     else       echo "Port 5000 (main): Not running";     fi;   else
    status=$(systemctl is-active algofactory-$port.service 2>/dev/null || echo "inactive");     if [ "$status" = "active" ]; then       pid=$(systemctl show algofactory-$port.service -p MainPID --value 2>/dev/null);       if [ "$pid" != "0" ] && [ -n "$pid" ]; then         mem=$(ps -p $pid -o %mem --no-headers 2>/dev/null | tr -d ' ');         rss=$(ps -p $pid -o rss --no-headers 2>/dev/null | tr -d ' ');         echo "Port $port: PID=$pid, Memory=${mem}%, RSS=${rss}KB";       else         echo "Port $port: Active but no PID found";       fi;     else       echo "Port $port: $status";     fi;   fi; done
systemctl is-active algofactory-1010.service algofactory-1011.service algofactory-1012.service algofactory-1014.service algofactory-1015.service
sudo systemctl start algofactory-1015.service && sleep 5 && systemctl is-active algofactory-1015.service
ls /etc/systemd/system/algofactory-*.service
sudo systemctl start algofactory-1010.service && sleep 8
echo "📊 MEMORY ANALYSIS WITH ONE ALGOFACTORY INSTANCE:"
echo "================================================"
free -h
echo
echo "🏭 AlgoFactory Instance Memory:"
pid=$(systemctl show algofactory-1010.service -p MainPID --value 2>/dev/null)
if [ "$pid" != "0" ] && [ -n "$pid" ]; then   mem=$(ps -p $pid -o %mem --no-headers 2>/dev/null | tr -d ' ');   rss=$(ps -p $pid -o rss --no-headers 2>/dev/null | tr -d ' ');   echo "Port 1010: PID=$pid, Memory=${mem}%, RSS=${rss}KB (~$((rss/1024))MB)"; else   echo "Instance not running or PID not found"; fi
echo
echo "🔥 Top Memory Consumers:"
ps aux --sort=-%mem | head -8
free -m && echo "---" && ps aux --sort=-%mem | head -5
echo 'Terminal capability test'
echo 'Terminal capability test'
