ls
sudo ls
root
sudo root
cd
cd..
sudo apt update
sudo apt upgrade -y
sudo apt full-upgrade -y
sudo reboot
scp -i ""C:\Users\<USER>\ubuntu-1-8gb.pem"" -r "C:\Users\<USER>\OneDrive\Desktop\git\algofactory" ubuntu@ip-172-31-12-60:/home/<USER>/
ls
scp -i "C:\Users\<USER>\ubuntu-1-8gb.pem" -r "C:\Users\<USER>\OneDrive\Desktop\git\algofactory" <EMAIL>:/home/<USER>/
scp -i ""C:\Users\<USER>\ubuntu-1-8gb.pem"" -r "C:\Users\<USER>\OneDrive\Desktop\git\algofactory" <EMAIL>:/home/<USER>/
scp -i "C:\Users\<USER>\ubuntu-1-8gb.pem" -r "C:\Users\<USER>\OneDrive\Desktop\git\algofactory" <EMAIL>:/home/<USER>/
bash
scp -i "C:\Users\<USER>\ubuntu-1-8gb.pem" -r "C:\Users\<USER>\OneDrive\Desktop\git\algofactory" <EMAIL>:/home/<USER>/
-i "C:\Users\<USER>\ubuntu-1-8gb.pem" -r "C:\Users\<USER>\OneDrive\Desktop\git\algofactory" <EMAIL>:/home/<USER>/
scp -i "C:\Users\<USER>\ubuntu-1-8gb.pem" -r "C:\Users\<USER>\OneDrive\Desktop\git\algofactory" <EMAIL>:/home/<USER>/
cd algofactory
python app.py
source ~/shared-venv/bin/activate
python app.py
sudo apt update
sudo apt install nginx -y
sudo systemctl status nginx
sudo nano /etc/nginx/sites-available/5000.algofactory.in
sudo nginx -t && sudo systemctl reload nginx
sudo certbot --nginx -d 5000.algofactory.in
sudo apt update
sudo apt install certbot python3-certbot-nginx -y
sudo certbot --nginx -d 5000.algofactory.in
sudo certbot --nginx -d 1010.algofactory.in
sudo nginx -t && sudo systemctl reload nginx
sudo nano /etc/nginx/sites-available/1010.algofactory.in
whoami
where
who
ls
sudo pip install gunicorn flask
sudo apt update
sudo apt install python3-pip -y
pip3 --version
sudo pip3 install flask gunicorn eventlet
sudo pip3 install flask gunicorn eventlet --break-system-packages
sudo pip3 install flask gunicorn eventlet --break-system-packages --ignore-installed
gunicorn app:app --bind 0.0.0.0:1010 --worker-class eventlet --daemon --chdir ~/algofactory-1010
nano ~/start-all.sh
chmod +x ~/start-all.sh
./start-all.sh
ps aux | grep gunicorn
sudo ss -tuln | grep :101
sudo ss -tuln | grep
gunicorn app:app --bind 0.0.0.0:1010 --worker-class eventlet --daemon --chdir ~/algofactory-1010
ps aux | grep gunicorn
sudo ss -tuln | grep :1010
sudo nano /etc/nginx/sites-available/1010.algofactory.in
sudo ln -s /etc/nginx/sites-available/1010.algofactory.in /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
https://1010.algofactory.in
sudo rm /etc/nginx/sites-enabled/default
sudo systemctl reload nginx
https://1010.algofactory.in
curl -I http://1010.algofactory.in
gunicorn app:app --bind 0.0.0.0:1010 --worker-class eventlet --daemon --chdir ~/algofactory-1010
ps aux | grep gunicorn
sudo ss -tuln | grep :1010
gunicorn app:app --bind 0.0.0.0:1010 --worker-class eventlet --chdir ~/algofactory-1010
sudo pip3 uninstall gunicorn
sudo systemctl disable gunicorn
sudo rm /etc/systemd/system/gunicorn.service
sudo systemctl daemon-reexec
bash
sudo pip3 uninstall gunicorn
sudo pip3 uninstall gunicorn --break-system-packages
pip3 list
sudo pip3 uninstall flask eventlet Werkzeug Jinja2 click blinker itsdangerous dnspython greenlet packaging rich PyJWT pyOpenSSL cryptography --break-system-packages
apt list --installed | grep rich
python3-rich/noble,now 13.7.1-1 all [installed,automatic]
sudo apt remove python3-rich
sudo apt autoremove
pip3 list
sudo reboot
cd ~algofactory
cd ~/algofactory
source ~/shared-venv/bin/activate
python app.py
cd.
cd /
cd algofactory-1010
ls
~/algofactory-1010
cd ~/algofactory-1010
python app.py
ls
sudo pip3 uninstall gunicorn
pip3 list | grep gunicorn
sudo pip3 uninstall gunicorn
bash
echo 'Terminal capability test'
echo 'Terminal capability test'
chmod +x ~/start.sh
nano ~/start.sh
ls
chmod +x ~/start.sh
chmod +x ~/start-all.sh
./start-all.sh
chmod +x ~/start.sh
./start.sh
ps aux | grep app.py
tail -n 30 ~/algofactory-1010/app.log
./start.sh
tail -n 30 ~/algofactory-100/app.log
./start.sh
tail -n 20 ~/algofactory-1010/app.log
./start.sh
tail -n 20 ~/algofactory-1010/app.log
./start.sh
tail -n 20 ~/algofactory-1010/app.log
./start.sh
tail -n 20 ~/algofactory-1010/app.log
ps aux | grep app.py
sudo pkill -f app.py
ps aux | grep app.py
pgrep -af app.py
tail -n 20 ~/algofactory-1010/app.log
pgrep -af app.py
sudo pkill -f app.py
pgrep -af app.py
sudo timedatectl set-timezone Asia/Kolkata
timedatectl
date
./start.sh
tail -n 20 ~/algofactory-1010/app.log
./start.sh
tail -n 20 ~/algofactory-1010/app.log
pgrep -af app.py
ps aux | grep app.py
sudo ss -tulnp | grep python
source /home/<USER>/shared-venv/bin/activate
pip install streamlit
python run_dashboard.py
ps aux | grep app.py
python stop_all.py
ps aux | grep app.py
tail -n 20 ~/algofactory-1010/app.log
python run_dashboard.py
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
cd ~/git
bash
source ~/shared-venv/bin/activate
python run_dashboard.py
cd /home/<USER>/git && python3 run_dashboard.py
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git && python3 run_dashboard.py
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -m streamlit run simple_dashboard.py --server.port 8502 --server.address 0.0.0.0 --server.headless true
sudo apt
source /home/<USER>/shared-venv/bin/activate && python3 -m streamlit run simple_dashboard.py --server.port 8502 --server.address 0.0.0.0 --server.headless true
sudo netstat -tlnp | grep :8502
ss -tlnp | grep :8502
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "import streamlit; print('Streamlit OK'); import simple_dashboard; print('Dashboard import OK')"
ps aux | grep streamlit
ss -tlnp | grep :8502
curl -I http://localhost:8502
sudo systemctl status nginx
curl -I http://dashboard.algofactory.in
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "from auth_config import auth_config; print('Auth enabled:', auth_config.is_auth_enabled())"
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
from auth_manager import AuthManager
auth = AuthManager()
success, message = auth.add_user('admin', 'AlgoFactory123!', 'admin', '<EMAIL>')
print(f'User creation: {success}')
print(f'Message: {message}')
if success:
    print('✅ Admin user created successfully!')
    print('Username: admin')
    print('Password: AlgoFactory123!')
"
sleep 5 && curl -I http://localhost:8502
ps aux | grep streamlit
ss -tlnp | grep :8502
curl -I http://localhost:8502
sudo nginx -t
ls -la /etc/nginx/sites-enabled/
sudo tail -10 /var/log/nginx/error.log
sudo rm /etc/nginx/sites-enabled/1010.algofactory.in
sudo nginx -t && sudo systemctl reload nginx
curl -I http://dashboard.algofactory.in
ls -la /etc/nginx/sites-enabled/
sudo cat /etc/nginx/sites-available/dashboard.algofactory.in.conf
curl -v http://127.0.0.1:8502
sudo tail -5 /var/log/nginx/dashboard.algofactory.in.error.log
sudo cp dashboard.algofactory.in.conf /etc/nginx/sites-available/ && sudo nginx -t && sudo systemctl reload nginx
cd /home/<USER>/git/nginx-config && sudo cp dashboard.algofactory.in.conf /etc/nginx/sites-available/ && sudo nginx -t && sudo systemctl reload nginx
curl -I http://dashboard.algofactory.in
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/dashboard && python3 -c "
from auth_manager import AuthManager
auth = AuthManager()

# Update the admin password
hashed_password = auth.hash_password('Ap9211###')
auth.config['users']['admin']['password_hash'] = hashed_password
auth.save_config()
print('✅ Admin password updated successfully!')
print('Username: admin')
print('Password: Ap9211###')
"
sleep 5 && curl -I http://localhost:8502
sudo systemctl stop nginx
sudo apt update && sudo apt install -y certbot
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
source /home/<USER>/shared-venv/bin/activate && python3 -c "import pandas_ta; print('✅ pandas_ta:', pandas_ta.__version__)"
source /home/<USER>/shared-venv/bin/activate && cd /home/<USER>/git/algofactory && python3 -c "import flask; print('Flask OK')"
echo 'Terminal capability test'
echo 'Terminal capability test'
echo 'Terminal capability test'
python3 -m http.server 8080
sudo python3 -m http.server 80
cd..
cd ~/algofactory
ubuntu
bash
echo 'Terminal capability test'
echo 'Terminal capability test'
free -h
sudo rm -f /etc/nginx/sites-enabled/backup.algofactory.in.conf /etc/nginx/sites-available/backup.algofactory.in.conf && sudo nginx -t && sudo systemctl reload nginx
sudo cp /tmp/dashboard.service /etc/systemd/system/algofactory-dashboard.service
sudo systemctl daemon-reload
sudo systemctl enable algofactory-dashboard.service
sudo systemctl start algofactory-dashboard.service
curl -I https://dashboard.algofactory.in
ss -tlnp | grep :5000
curl -I http://localhost:5000
sudo nginx -t && sudo systemctl reload nginx
curl -I http://5000.algofactory.in
cat > /tmp/algofactory.service << 'EOF'
[Unit]
Description=AlgoFactory - Flask Trading Application
After=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/git/algofactory
Environment=PATH=/home/<USER>/shared-venv/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/home/<USER>/shared-venv/bin/python3 app.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

cat > /tmp/dashboard.service << 'EOF'
[Unit]
Description=AlgoFactory Dashboard - Streamlit Application
After=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/git/dashboard
Environment=PATH=/home/<USER>/shared-venv/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/home/<USER>/shared-venv/bin/python3 -m streamlit run simple_dashboard.py --server.port 8502 --server.address 0.0.0.0 --server.headless true
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

ps aux --sort=-%mem | head -15
ps aux | grep -E "(streamlit|python.*app\.py|flask|algofactory)" | grep -v grep
ss -tlnp | grep -E ":(5000|8502|8080|443|80)"
rm -f /home/<USER>/algofactory-backup-*.tar.gz /home/<USER>/backup-part-* && rm -rf /tmp/backup
pkill -f "streamlit\|flask\|app\.py" 2>/dev/null || echo "No streamlit/flask processes found"
sudo lsof -i :5000 -i :8502 2>/dev/null || echo "No processes on ports 5000 or 8502"
ss -tlnp
free -h && echo "=== Top Memory Consumers ===" && ps aux --sort=-%mem | head -8
sudo fallocate -l 1G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
free -h
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
sudo sysctl vm.swappiness=10
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
sudo cp /tmp/algofactory-dashboard.service /etc/systemd/system/
sudo cp /home/<USER>/algofactory-dashboard.service /etc/systemd/system/
sudo tee /etc/systemd/system/algofactory-dashboard.service > /dev/null << 'EOF'
[Unit]
Description=AlgoFactory Dashboard - Streamlit Application
Documentation=https://streamlit.io/
After=network.target
Wants=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/git/dashboard
Environment=PATH=/home/<USER>/shared-venv/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ExecStart=/home/<USER>/shared-venv/bin/python3 -m streamlit run simple_dashboard.py --server.port 8502 --server.address 0.0.0.0 --server.headless true
ExecReload=/bin/kill -HUP $MAINPID
KillMode=mixed
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=algofactory-dashboard

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=false
ReadWritePaths=/home/<USER>/git/dashboard
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# Resource limits
LimitNOFILE=65536
MemoryMax=300M

[Install]
WantedBy=multi-user.target
EOF

curl -I -H "Host: 5000.algofactory.in" http://**************
ls -la /etc/nginx/sites-enabled/
cd /home/<USER>/git/nginx-config && sudo cp 5000.algofactory.in-http.conf /etc/nginx/sites-available/ && sudo nginx -t
sudo systemctl reload nginx
curl -I http://**************
curl -s http://************** | head -10
echo 'Terminal capability test'
echo 'Terminal capability test'
sudo nginx -t
sudo systemctl start nginx
curl -I https://5000.algofactory.in
curl -I http://5000.algofactory.in
ss -tlnp | grep -E ":(5000|8502|8765|5555)"
sudo systemctl restart algofactory-dashboard.service
sleep 3 && curl -I https://dashboard.algofactory.in
